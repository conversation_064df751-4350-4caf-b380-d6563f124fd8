# Go workspace file
go.work

# Environment variables file
.env

# Binary output directory
/bin/

# IDE-specific files
.idea/
.vscode/

# OS-specific files
.DS_Store
Thumbs.db

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Configuration files
/configs/.local.env
