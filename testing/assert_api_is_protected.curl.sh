#!/bin/bash

function call_url_without_api_key(){
    printf "Expecting: \"Unauthorized: Authorization header missing\"\n"
    curl -X POST http://localhost:8000/links \
        -H "Content-Type: application/json" \
        -d '{
            "targetUrl": "https://zdnet.de"
        }'
}

function call_unprotected_endpoint() {
    printf "Calling unprotected endpoint\n"
    curl -X GET http://localhost:8000/.well-known/alive \
        -H "Content-Type: application/json"
        
}
function main(){
    call_url_without_api_key
    call_unprotected_endpoint
}

main

