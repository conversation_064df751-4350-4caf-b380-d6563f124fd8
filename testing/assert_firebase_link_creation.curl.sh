#!/bin/bash

# Create a Dynamic Link without source url pre-defined
function create_dynamic_link_without_source(){
  local msg="Create a Dynamic Link without a pre-defined source url ..."
  printf "%s\n" "${msg}"

  curl -k -X POST http://localhost:8000/links \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://stagedates.com/events/star-fm-rooftop-party-nhow-berlin-20250726-vDO1Q"    
  }'
}

function create_dynamic_link_with_source(){
  local msg="Create a Dynamic Link with a pre-defined source url ..."
  printf "%s\n" "${msg}"

  curl -k -X POST http://localhost:8000/links \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://stagedates.com/events/star-fm-rooftop-party-nhow-berlin-20250726-vDO1Q",
    "shortlink": "https://link.dev.stagedat.es/abcd"
  }'

}

create_dynamic_link_without_source
create_dynamic_link_with_source
