#!/bin/bash

function usage(){
    echo "Error: target_testing_env argument is required"
    echo "Usage: $0 <local|dev>"
    echo "  local - Use http://localhost:8000"
    echo "  dev   - Use https://link.dev.stagedat.es"
    exit 1
}

# Check if target environment argument is provided
if [ -z "$1" ]; then
    usage && exit 1
fi

target_testing_env=$1

# Set TESTING_BASE_URL based on target environment
if [ "$target_testing_env" = "local" ]; then
    export TESTING_BASE_URL="http://localhost:8000"
elif [ "$target_testing_env" = "dev" ]; then
    export TESTING_BASE_URL="https://link.dev.stagedat.es"
else
    usage && exit 1
fi

echo "Using environment: $target_testing_env"
echo "Testing URL: $TESTING_BASE_URL"

function call_url(){
    local url=$1
    shift
    local msg=$1
    printf "Test Description: %s:\n%s\n" "${msg}" "${url}"
    curl -k -i $url
    echo
}

function test_existing_link(){
    call_url "${TESTING_BASE_URL}/jcoM" "Asserting existing dynamic short link behaviour"
}

function test_non_existing_link(){
    call_url ${TESTING_BASE_URL}/jcoMXXX "Asserting non-existing short link"
}

function main(){
    test_existing_link
    test_non_existing_link
}

main
