#!/bin/bash

# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Version:                                                                    03 ║
# ║ Date of Version:                                                    22.05.2025 ║
# ║ Owner:                                                                      SD ║
# ║ Classification:                                                       Internal ║
# ║ Distribution:                                                        All Staff ║
# ╚════════════════════════════════════════════════════════════════════════════════╝

set -eu -o pipefail
set -o posix
# set -o errexit   # abort on nonzero exitstatus
# set -o nounset   # abort on unbound variable
# set -o pipefail  # don't hide errors within pipes

script_path=$(realpath "${BASH_SOURCE[0]}")
script_path="${script_path%/*}"
cd "${script_path}"

source ./.env

{
    function usage(){
        printf "Usage:\n"
        printf "\t%s <subcommand>\n\n" ${0}
        printf "Subcommands:\n"
        printf "\t%10s\t\t%s\n" "--start-docker-compose-env" "Shoul be the very first step. Start a local postgresql and pubsub emulator in a docker compose environment"
        printf "\t%10s\t%s\n" "--start-local-short-link-service" "Starts the service locally, plain, without docker"
        echo
        printf "\t%10s\t\t%s\n" "--print-some-useful-endpoints" "Print some useful endpoints"
        echo
        printf "\nExperimental:\n"
        printf "\t%10s\t\t%s\n" "--init-local-db-for-shortlink-svc-dev-works" "..."
        printf "\t%10s\t\t\t%s\n" "--init-pubsub-emulator" "Add needed contents to local plain pubsub emulator"
        printf "\t%10s\t\t%s\n" "--start-local-pubsub-emulator" "Optionally starts a plain emulator instead of docker compose based"
        printf "\t%10s\t\t%s\n" "--forward-events-service" "Forward events service port to localhost"
      
        printf "\t%10s\t\t%s\n" "--start-docker-build-image" "Locally test building docker image"
	
    }

    function __init(){
        ln -s $HOME/s/tools_local/dev-environment/docker-compose.yaml
    }

    # TODO: add to: ~/s/tools_local/dev-environment/.env
    # TODO: followings have moved to .env. Deal with them later.
    # export PUBSUB_PORT="8087"
    # export PUBSUB_HOST="0.0.0.0"
    # export PUBSUB_HOST_PORT="${PUBSUB_HOST}:${PUBSUB_PORT}"


    # Inject  ~/s/tools_local/dev-environment/.env configs:
    # local dev db configuration
    # TODO: followings have moved to .env. Deal with them later.
    # export POSTGRES_USER=postgres
    # export POSTGRES_PASSWORD=postgres
    # export POSTGRES_DB=backend

    # SQL_DUMP=<path to backend_dev.sql dump>
    #GCLOUD_PROJECT=demo-prj
    # export GCLOUD_PROJECT=stdts-dev

    # export CONTAINER_NAME_PREFIX="shortlink-svc"

    function start_docker_compose_env(){
        source ./.env
        docker compose -f docker-compose.yaml up -d
    }
    function start_docker_build_image(){
        source ./.env
        docker build -t shortlink-test-build -f infrastructure/Dockerfile .
    }

    function start_pubsub_emulator_plain(){
        echo "Going to listen on ${PUBSUB_HOST_PORT} ..."
        gcloud beta emulators pubsub start \
            --host-port=${PUBSUB_HOST_PORT} \
            --data-dir=$PWD/mtdata/pubsubemulator
    }

    function start_pubsub_emulator_containerized(){
        # TODO: add data-dir param and bind-mount
        docker run --name=gcloud-emulator -d -p ${PUBSUB_PORT}:${PUBSUB_PORT} \
            gcr.io/google.com/cloudsdktool/google-cloud-cli:emulators gcloud beta emulators pubsub start --project=stdts-dev \
            --host-port=${PUBSUB_HOST_PORT}
    }

    function start_local_service(){
        exec gow run cmd/web/*.go
    }

    function init_pubsub_emulator(){
        PUBSUB_SCRIPT_PATH=$PWD/mtsrc/python-pubsub/samples/snippets
        TOPIC_ID=events.events
        SUB_ID=shortlink-worker.events.events

        echo "creating topics ..."
        python ${PUBSUB_SCRIPT_PATH}/publisher.py ${GCLOUD_PROJECT} create ${TOPIC_ID}
        
        echo "creating subscriptions ..."
        # TODO: continue here
        # ${SUB_ID} --topic=${TOPIC_ID}
        python subscriber.py ${GCLOUD_PROJECT} create generate-pdf-v1 eventarc-europe-west3-generate-pdf-function-293018-sub-782
    }

    function init_local_db_for_shortlink_svc_dev_works(){
        ## This Script content is originally from minikube:start.sh:initial_backend_setup()
        echo working on host $POSTGRES_HOST and port  $POSTGRES_PORT ...

        # close connections, so that we can re-create the db
        psql -U postgres -h $POSTGRES_HOST -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '"${POSTGRES_DB}"' AND leader_pid IS NULL;"

        psql -U postgres -h $POSTGRES_HOST -c "DROP DATABASE IF EXISTS backend;"
        psql -U postgres -h $POSTGRES_HOST -c "CREATE DATABASE backend;"

        # change the search_path, so that we work in the appropriate schema
        psql -U postgres -h $POSTGRES_HOST -d backend -c "ALTER ROLE ${POSTGRES_USER} SET search_path=shortlink;"
        psql -U postgres -h $POSTGRES_HOST -d backend -c "CREATE SCHEMA IF NOT EXISTS shortlink AUTHORIZATION ${POSTGRES_USER};"

        # in case we need to restore a dump
        # psql -U postgres -h $POSTGRES_HOST -d backend <./mt/db/backend_dev.sql >/dev/null 2>&1
    }

    function forward_events_service(){
        echo "Forwarding events service port to localhost ..."
        kubectl $CONTEXT -nstdts-dev port-forward svc/events-gke 9090:8080
    }

    function print_some_useful_endpoints(){
        printf "http://localhost:9092/health\n"
        printf "http://localhost:9090/events/api/health\n"
        printf "http://localhost:9090/events/api/swaggerui\n"
        printf "http://localhost:9090/events/api/events/8ca79956-8480-451f-afa8-07ab575b41a3\n"
        printf "http://localhost:9092/e/8ca79956-8480-451f-afa8-07ab575b41a3\n"
    }

    function import_testing_db_dump(){
        source ./.env
        echo "Importing testing db dump ..."
        psql -U postgres -h ${POSTGRES_HOST} -d backend -f ./local/init-db.sql
    }

    function fix_and_tidy_go_modules(){
        echo "Tidying go modules ..."
        go clean -modcache
        go get -u ./...
        go mod tidy
    }

    function main(){
        case ${1-usage} in
            --start-docker-compose-env)
                start_docker_compose_env
                sleep 2
                import_testing_db_dump
                sleep 2
                fix_and_tidy_go_modules
                sleep 2
                forward_events_service
                
                ;;

            --init-pubsub-emulator)
                init_pubsub_emulator
                ;;

            --start-local-short-link-service)
                start_local_service
                ;;

            --start-local-pubsub-emulator)
                start_pubsub_emulator_plain
                ;;

            --start-docker-build-image)
                start_docker_build_image
                ;;
	    
            --forward-events-service)
                forward_events_service
                ;;

            --print-some-useful-endpoints)
                print_some_useful_endpoints
                ;;

            --init-local-db-for-shortlink-svc-dev-works)
                init_local_db_for_shortlink_svc_dev_works
                ;;
        *)
            usage
            ;;
        esac
    }
}

{
    main "${@}"
    exit 0
}
