package main

import (
	"github.com/stagedates/shortlink-service/pkg/handlers"
	"gofr.dev/pkg/gofr"
	// "gofr.dev/pkg/gofr"
)

func initRoutes(app *gofr.App) {
	LogInfo(app, "initRoutes(): Registering routes...")
	// Register the preview link handler with the event client
	app.GET("/e/{identifier}", handlers.EventPreviewLinkHandler(app))

	//gofrapp.GET("/healthz", handlers.HealthHandler)
	//gofrapp.GET("/health", handlers.HealthHandler)
	app.GET("/", handlers.FirebaseLinkHandler)
	app.GET("/{identifier}", handlers.FirebaseLinkHandler)
	app.POST("/links", handlers.FirebaseLinkCreationHandler(app))

	LogInfo(app, "initRoutes(): done registering routes.")
}
