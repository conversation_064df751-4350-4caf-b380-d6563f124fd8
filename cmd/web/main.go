package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"log/slog"
	"net/http"
	"os"
	"time"

	"github.com/stagedates/shortlink-service/migrations"
	"github.com/stagedates/shortlink-service/pkg/config"
	"github.com/stagedates/shortlink-service/pkg/handlers"
	"github.com/stagedates/shortlink-service/pkg/render"
	"github.com/stagedates/shortlink-service/pkg/serviceclients"

	"github.com/alexedwards/scs/v2"

	// "gofr.dev/examples/using-publisher/migrations"
	// "gofr.dev/pkg/gofr/migration"

	"github.com/redis/go-redis/v9"
	"gofr.dev/pkg/gofr"
	gofrHTTP "gofr.dev/pkg/gofr/http"
)

var ac config.AppConfig
var session *scs.SessionManager
var redisClient *redis.Client

const (
	ExitServiceUnavailable = 1
	ExitConfigError        = 2
	ExitNetworkError       = 3
	// ... other exit codes as needed ...
)

var RequestApiKey string

// Define custom middleware function to access headers and set context
func customMiddleware() gofrHTTP.Middleware {
	return func(inner http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			apiKey := r.Header.Get("X-Api-Key")

			logger := slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
				Level: slog.LevelDebug,
			}))
			logger.Debug("received request with API key", "apiKey", apiKey)

			// Set the API key in the request context for gofr
			ctx := r.Context()
			ctx = context.WithValue(ctx, "RequestApiKey", apiKey)
			r = r.WithContext(ctx)

			inner.ServeHTTP(w, r)
		})
	}
}

// main is the main function
func main() {
	// change this to true when in production
	ac.InProduction = false

	// set up the session
	session = scs.New()
	session.Lifetime = 24 * time.Hour
	session.Cookie.Persist = true
	session.Cookie.SameSite = http.SameSiteLaxMode
	session.Cookie.Secure = ac.InProduction

	ac.Session = session

	tc, err := render.CreateTemplateCache()
	if err != nil {
		log.Fatal("cannot create template cache")
	}

	ac.TemplateCache = tc
	ac.UseCache = false

	repo := handlers.NewRepo(&ac)
	handlers.NewHandlers(repo)

	render.NewTemplates(&ac)

	app := gofr.New()
	app.UseMiddleware(customMiddleware())

	// Run PostgreSQL migrations
	app.Migrate(migrations.All())

	// Setup PubSub processing
	// if err := setupPubSubProcessing(gofrapp, ctx, eventsClient, errChan, cancel); err != nil {
	// 	LogError(gofrapp, "Failed to set up Pub/Sub processing: %v", err)
	// }

	// Start the web server
	LogInfo(app, "Starting web server in GoFr mode...")

	// Register routes before starting the server
	initRoutes(app)

	// Start the main loop, this will run until SIGINT or SIGTERM is received
	// Add a new cancel context to handle teardown of extra applications, eg. Pub/Sub processing
	app.Run()
}

// setupPubSubProcessing initializes and configures PubSub message processing
func setupPubSubProcessing(gofrapp *gofr.App, ctx context.Context, errChan chan error, cancel context.CancelFunc) error {
	// Start Pub/Sub processing in a separate goroutine
	go func() {
		LogInfo(gofrapp, "Starting Pub/Sub message processing...")
		err := handlers.HandleMessagesWithGooglesPubSubClient(gofrapp, ctx, "shortlink-worker.events.events", "stdts-dev", func(msg []byte) {
			// Parse message to check operation type
			var payload struct {
				Data struct {
					Operation string `json:"operation"`
				} `json:"data"`
			}

			if err := json.Unmarshal(msg, &payload); err == nil {
				switch payload.Data.Operation {
				case "update", "createEvent":
					main_event_processing_from_web(gofrapp, msg)
				default:
					LogInfo(gofrapp, "Skipping message with operation: %s", payload.Data.Operation)
				}
			}
		})
		if err != nil {
			errChan <- fmt.Errorf("pub/sub processing error: %v", err)
			cancel()
		}
	}()

	return nil
}

// TODO: move to better location
func main_event_processing_from_web(gofrapp *gofr.App, msg []byte) {
	var payload struct {
		Data struct {
			ID string `json:"id"`
		} `json:"data"`
	}

	if err := json.Unmarshal(msg, &payload); err == nil && payload.Data.ID != "" {
		LogInfo(gofrapp, "ID: %s\n", payload.Data.ID)

		// Fetch event details using GetEvent
		eventsClient, err := serviceclients.NewEventsClient(gofrapp.Config.GetOrDefault("EVENTS_SERVICE_BASE_URL", "http://localhost:8080"))
		event, err := eventsClient.GetEvent(gofrapp, payload.Data.ID)
		if err != nil {
			gofrapp.Logger().Errorf("Error getting event: %v", err)
		} else {
			LogInfo(gofrapp, "\n--- Event Details ---")
			LogInfo(gofrapp, "Title: %s", event.Title)
			LogInfo(gofrapp, "Subtitle: %s", event.Subtitle)
			LogInfo(gofrapp, "Description: %s", event.Description)
			LogInfo(gofrapp, "Thumbnail URL: %s", event.ThumbnailURL)
			LogInfo(gofrapp, "Cover URL: %s", event.CoverURL)
			LogInfo(gofrapp, "Slug: %s", event.Slug)
			LogInfo(gofrapp, "Thumbnail Path: %s", event.ThumbnailPath)
			LogInfo(gofrapp, "Is Draft: %v", event.IsDraft)

			LogInfo(gofrapp, handlers.OpenGraph(event.Title, event.Description, event.ThumbnailURL, event.Slug))
		}
	}
}

// TODO: move to better location
func main_event_processing_from_pubsub(gofrapp *gofr.App) error {
	ctx := context.Background()
	err := handlers.HandleMessagesWithGooglesPubSubClient(gofrapp, ctx, "shortlink-worker.events.events", "stdts-dev", func(msg []byte) {
		main_event_processing_from_web(gofrapp, msg)
	})
	if err != nil {
		gofrapp.Logger().Fatalf("Error handling messages: %v", err)
		return err
	}
	return nil
}

// setupRedisConnection initializes and verifies the Redis connection
func setupRedisConnection(gofrapp *gofr.App) (*redis.Client, error) {
	redisEndpoint := gofrapp.Config.GetOrDefault("REDIS_ENDPOINT_HOST_AND_PORT", "localhost:6379")

	client := redis.NewClient(&redis.Options{
		Addr: redisEndpoint,
	})

	// Verify Redis connection
	if _, err := client.Ping(context.Background()).Result(); err != nil {
		gofrapp.Logger().Errorf("Failed to connect to Redis: %v", err)
		return nil, err
	}

	gofrapp.Logger().Info("Successfully connected to Redis")
	return client, nil
}
