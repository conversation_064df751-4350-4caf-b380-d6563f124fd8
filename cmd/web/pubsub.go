package main

/* func InitPubSubSubsystem(gofrapp *gofr.App) {
	// Configure PubSub
	pubsubConfig := pubsub.Config{
		Driver: "google-pubsub",
		URL:    os.Getenv("PUBSUB_EMULATOR_HOST"),
	}

	// Add PubSub to the application
	err := gofrapp.AddPubSub(pubsubConfig)
	if err != nil {
		fmt.Printf("Failed to add PubSub: %v\n", err)
		os.Exit(1)
	}

	// Subscribe to the topic
	app.Subscribe("projects/stdts-dev/topics/events.events", handleMessage)

} */
