# This file configures the application at runtime
# Ref.: https://gofr.dev/docs/references/configs

APP_NAME="my-app-name"

# GoFr enabled app listens on this port:
HTTP_PORT=8080
GOFR_ENABLED=true
LOG_LEVEL=INFO # INFO, WARN,DEBUG,ERROR,NOTICE or FATAL
TRACE_EXPORTER=otlp
TRACER_URL=localhost:4317
DB_HOST=127.0.0.1
DB_PORT=5432
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=your_database_name
DB_DIALECT=postgres
DB_OPTIONS="search_path=my_schema_name"
DB_MAX_IDLE_CONNECTION=5
DB_MAX_OPEN_CONNECTION=10

EVENTS_ENDPOINT_HOST_AND_PORT="http://localhost:9090"


REDIS_ENDPOINT_HOST_AND_PORT="localhost:6379"
REDIS_PASSWORD="password"

SHORTLINK_BASE_URL="http://localhost:8000

# API_KEYS is a coma separated list of base64 encoded api keys
API_KEYS="XXX"
