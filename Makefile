# https://clarkgrubb.com/makefile-style-guide

# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Version:                                                                   0.1 ║
# ║ Date of Version:                                                    31.10.2024 ║
# ║ Owner:                                                                      SD ║
# ║ Classification:                                                       Internal ║
# ║ Distribution:                                                        All Staff ║
# ╚════════════════════════════════════════════════════════════════════════════════╝

MAKEFLAGS += --warn-undefined-variables --no-builtin-rules
SHELL := /usr/bin/env bash
.SHELLFLAGS := -uo pipefail -c
.DEFAULT_GOAL := help
CHANGELOG_FILE = CHANGELOG.md
VERSION_PREFIX = v


.PHONY: help
help:
	@echo "Usage: make <start|tidy>"
	@echo "start:		Start the server"
	@echo "tidy:		Tidy the modules setup"

.PHONY: start
start:
	APP_ENV="local" go run cmd/web/*.go

.PHONY: tidy
tidy:
	go mod tidy

.PHONY: docker-build
docker-build:
	docker build -t shortlink-service . -f ./infrastructure/Dockerfile

.PHONY: docker-run
docker-run:
	docker run -it --rm -p 8080:8080 --name my-shortlink-service shortlink-service


changelog:  ## Generate changelog using git-cliff
	$(eval NEW_VERSION := $(shell GIT_CLIFF_CONFIG=.cliff.toml $(HOME)/.cargo/bin/git-cliff --bumped-version | sed 's/^v//'))
	@GIT_CLIFF_CONFIG=.cliff.toml $(HOME)/.cargo/bin/git-cliff --bumped-version --output $(CHANGELOG_FILE) --tag $(VERSION_PREFIX)$(NEW_VERSION)
	@echo "Generated changelog at: $(CHANGELOG_FILE)"
