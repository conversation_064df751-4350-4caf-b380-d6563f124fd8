version: '3.8'

services:

  db:
    image: docker.io/postgres:latest
    container_name: ${CONTAINER_NAME_PREFIX}-postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      PGUSER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      # - ./roles.sql:/docker-entrypoint-initdb.d/00-roles.sql:ro
      #- ${GRANT}:/docker-entrypoint-initdb.d/01-grant.sql:ro
      #- ${SQL_DUMP}:/docker-entrypoint-initdb.d/02-init.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "sh -c 'pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}'"]
      interval: 5s
      timeout: 5s
      retries: 3
      start_period: 15s
    restart: always
    ports:
      # - "${POSTGRES_PORT}:${POSTGRES_PORT}"
      - "5432:5432"
    networks:
      - shortlinks-service

  pgadmin:
    image: dpage/pgadmin4
    container_name: ${CONTAINER_NAME_PREFIX}-pgadmin4
    ports:
      - 8080:80
    # For local development a separate pgadmin4 password is not needed
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_CONFIG_SERVER_MODE=False
      - PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED=False
    restart: unless-stopped
    volumes:
      - ./servers.json:/pgadmin4/servers.json
    networks:
      - shortlinks-service

  ### REDIS ###
  redis:
    image: docker.io/redis:latest
    container_name: ${CONTAINER_NAME_PREFIX}-redis
    ports:
      - ${REDIS_PORT:-6379}:6379
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    restart: always
    networks:
      - shortlinks-service

volumes:
  pgadmin:
  postgres_data:

networks:
  shortlinks-service:
