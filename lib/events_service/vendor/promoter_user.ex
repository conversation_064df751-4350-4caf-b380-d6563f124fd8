defmodule EventsService.Vendor.PromoterUser do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "promoter_users" do
    field :user_document_id, :string
    field :user_id, Ecto.UUID
    belongs_to :promoter, EventsService.Vendor.Promoter
    field :deleted_at, :utc_datetime

    timestamps()
  end

  @doc false
  def changeset(promoter_user, attrs) do
    promoter_user
    |> cast(attrs, [:user_id, :promoter_id, :user_document_id, :deleted_at])
    |> validate_required([:user_document_id, :promoter_id])
  end
end
