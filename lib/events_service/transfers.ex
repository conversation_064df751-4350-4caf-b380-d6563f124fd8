defmodule EventsService.Transfers do
  @moduledoc """
  The Transfers context.
  """

  import Ecto.Query, warn: false

  alias Adyen.Services.BalancePlatform
  alias Adyen.Services.LegalEntityManagement
  alias Adyen.Services.Transfer
  alias Ecto.Multi
  alias EventsService.Addresses.Address
  alias EventsService.Events
  alias EventsService.Events.Event
  alias EventsService.Events.TicketCategory
  alias EventsService.Events.Variant
  alias EventsService.Events.VariantCounter
  alias EventsService.Location
  alias EventsService.Location.Venue
  alias EventsService.Pubsub.Publisher.PayoutInvoicePublisher
  alias EventsService.Repo
  alias EventsService.Transfers.PayoutInvoice
  alias EventsService.Transfers.PayoutInvoiceLine
  alias EventsService.Transfers.PayoutTransactions
  alias EventsService.Transfers.TransactionHistory
  alias EventsService.Util.DateTime, as: DateTimeUtil
  alias EventsService.Util.Money
  alias EventsService.Vendor
  alias EventsService.Vendor.Promoter
  alias ExServiceClient.Services.AccountsService

  require Logger

  @allowed_tax_rates [0.07, 0.19]

  @doc """
  Returns the list of payout_transactions.

  ## Examples

      iex> list_payout_transactions()
      [%PayoutTransactions{}, ...]

  """
  def list_payout_transactions(event_id) do
    open_request =
      from(pt in PayoutTransactions,
        left_join: pi in assoc(pt, :invoice),
        on: pi.payout_transaction_id == pt.id and is_nil(pi.deleted_at),
        where: pt.event_id == ^event_id,
        preload: [invoice: pi]
      )

    Repo.all(open_request)
  end

  def calculate_open_payout_transactions(event_id) do
    open_request =
      from(pt in PayoutTransactions,
        where: pt.event_id == ^event_id,
        where: pt.status in [:AUTHORISED],
        select: sum(pt.amount)
      )

    open_request
    |> Repo.one()
    |> case do
      nil -> 0
      amount -> amount
    end
  end

  def calculate_closed_payout_transactions(event_id) do
    finished_request =
      from(pt in PayoutTransactions,
        where: pt.event_id == ^event_id,
        where: pt.status in [:BOOKED],
        select: sum(pt.amount)
      )

    finished_request
    |> Repo.one()
    |> case do
      nil -> 0
      amount -> amount
    end
  end

  @spec event_promoter_is_from_germany?(promoter :: Promoter.t()) :: boolean()
  def event_promoter_is_from_germany?(%Promoter{address: %{country_iso: "DE"}} = _promoter), do: true
  def event_promoter_is_from_germany?(_promoter), do: false

  @spec event_venue_in_germany?(event_id :: Ecto.UUID.t()) :: boolean()
  def event_venue_in_germany?(event_id) do
    case Location.get_venue_by_event_id(event_id, [:address]) do
      %Venue{address: %Address{country_iso: "DE"}} -> true
      _ -> false
    end
  end

  @spec event_has_only_valid_tax_rate?(event_id :: Ecto.UUID.t()) :: boolean()
  def event_has_only_valid_tax_rate?(event_id) do
    query =
      from(tc in TicketCategory,
        join: v in Variant,
        on: v.ticket_category_id == tc.id,
        where:
          tc.event_id == ^event_id and tc.tax_rate not in @allowed_tax_rates and is_nil(tc.deleted_at) and
            is_nil(v.deleted_at),
        select: count(tc.id)
      )

    case Repo.one(query) do
      0 -> true
      _ -> false
    end
  end

  @doc """
  Gets a single payout_transactions.

  Raises `Ecto.NoResultsError` if the Payout transactions does not exist.

  ## Examples

      iex> get_payout_transactions!(123)
      %PayoutTransactions{}

      iex> get_payout_transactions!(456)
      ** (Ecto.NoResultsError)

  """
  def get_payout_transaction(id) do
    case Ecto.UUID.dump(id) do
      {:ok, _id} ->
        Logger.debug("Fetching payout transaction with id #{id}")
        Repo.one(from(pt in PayoutTransactions, where: pt.id == ^id))

      _ ->
        Logger.warning("Could not cast id #{id} to UUID")
        nil
    end
  end

  @spec prepare_payout_transactions_params(
          payout_transactions_params :: map(),
          user_id :: Ecto.UUID.t(),
          event_id :: Ecto.UUID.t(),
          user_amount :: integer(),
          balance_account_id :: String.t(),
          promoter_id :: Ecto.UUID.t()
        ) :: map()
  def prepare_payout_transactions_params(
        payout_transactions_params,
        user_id,
        event_id,
        user_amount,
        balance_account_id,
        promoter_id
      ) do
    Map.merge(payout_transactions_params, %{
      "created_by" => user_id,
      "event_id" => event_id,
      "amount" => user_amount,
      "balance_account_id" => balance_account_id,
      "promoter_id" => promoter_id
    })
  end

  @doc """
  Creates a payout_transactions.

  ## Examples

      iex> create_payout_transactions(%{field: value})
      {:ok, %PayoutTransactions{}}

      iex> create_payout_transactions(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_payout_transaction(attrs \\ %{}) do
    case create_pre_init_payout_transaction(attrs) do
      {:ok, payout_transaction} ->
        process_payout_transaction(payout_transaction)

      {:error, error} ->
        {:error, :create_pre_init_payout_transaction, error}
    end
  end

  @doc """
   Moved the process_payout_transaction function to a separate function because in some cases Adyen will not send
   the reference which is needed to fetch the payout transaction and update it.
  """
  @spec process_payout_transaction(PayoutTransactions.t()) :: {:ok, PayoutTransactions.t()} | {:error, atom, map}
  def process_payout_transaction(%PayoutTransactions{id: payout_transaction_id} = payout_transaction) do
    with {_, {:ok, result}} <- {:execute_payout, execute_payout(payout_transaction)},
         {_, {:ok, updated_payout_transaction}} <-
           {:update_payout_transaction, update_payout_transaction(payout_transaction, result)} do
      {:ok, updated_payout_transaction}
    else
      {:execute_payout,
       {:error,
        %{
          message: %{"requestId" => psp_reference} = msg,
          status: _status
        } = error}} ->
        Logger.error("Failed to execute payout transaction with psp reference #{psp_reference}")
        payout_transaction = get_payout_transaction(payout_transaction_id)

        update_failed_payout_transaction(payout_transaction, %{
          "status" => "FAILED",
          "psp_reference" => psp_reference,
          "psp_result" => msg
        })

        {:error, :execute_payout, error}

      {:execute_payout, error} ->
        Logger.error("Failed to execute payout transaction with error #{inspect(error)}")
        {:error, :execute_payout, error}

      {:update_payout_transaction, {:error, error}} ->
        Logger.error("Failed to update payout transaction with error #{inspect(error)}")
        {:error, :update_payout_transaction, error}
    end
  end

  @spec create_pre_init_payout_transaction(map()) ::
          {:ok, PayoutTransactions.t()}
          | {:error, atom, any}
          | {:error, atom}
  def create_pre_init_payout_transaction(%{"promoter_id" => promoter_id, "event_id" => event_id} = attrs) do
    context = %{promoter_id: promoter_id, event_id: event_id}
    payout_invoice_enabled = Unleash.enabled?(:enable_payout_invoice_generation, context)

    with {_, %Promoter{short_code: short_code, is_small_business: is_small_business}} <-
           {:get_promoter, Vendor.get_promoter_by_id(promoter_id)},
         {_, total_ticket_tax_rate_based} <-
           {:total_tickets_by_tax_rate, sum_total_ticket_sold_for_each_tax_rate(event_id)},
         total_ticket_sold = Enum.reduce(total_ticket_tax_rate_based, 0, fn %{total: total}, acc -> total + acc end),
         {_, {:ok, %{insert_transaction: payout_transaction}}} <-
           {:do_create,
            do_create_pre_init_payout_transaction(
              attrs,
              total_ticket_sold,
              short_code,
              total_ticket_tax_rate_based,
              is_small_business,
              payout_invoice_enabled
            )} do
      {:ok, payout_transaction}
    else
      {:get_promoter, nil} ->
        {:error, :promoter_not_found}

      {:total_tickets_by_tax_rate, error} ->
        {:error, :failed_to_get_total_tickets_by_tax_rate, error}

      {:do_create, {:error, failed_operation, failed_value, _changes_so_far}} ->
        Logger.error(
          "Failed to create payout transaction in step #{inspect(failed_operation)} and error #{inspect(failed_value)}"
        )

        {:error, failed_value}
    end
  end

  @spec sum_total_ticket_sold_for_each_tax_rate(event_id :: Ecto.UUID.t()) :: list(map())
  def sum_total_ticket_sold_for_each_tax_rate(event_id) do
    Repo.all(
      from(tc in TicketCategory,
        join: v in Variant,
        on: v.ticket_category_id == tc.id,
        join: vc in VariantCounter,
        on: vc.variant_id == v.id,
        where:
          tc.event_id == ^event_id and tc.tax_rate in @allowed_tax_rates and is_nil(tc.deleted_at) and
            is_nil(v.deleted_at),
        group_by: tc.tax_rate,
        select: %{tax_rate: tc.tax_rate, total: sum(coalesce(vc.sold, 0))}
      )
    )
  end

  def execute_payout(%PayoutTransactions{event_id: event_id, id: id} = payout_transaction) do
    case Events.get_event(event_id) do
      %Event{title: title, start_date: start_date} ->
        Transfer.transfer_funds(
          :payout_to_transfer_instrument,
          Map.merge(payout_transaction, %{
            reference_for_beneficiary: event_id,
            reference: id,
            description: "Payout for #{title} - #{EventsService.Util.DateTime.to_default_string(start_date)}"
          })
        )

      nil ->
        {:error, :event_not_found}

      _ ->
        {:error, :invalid_event_data}
    end
  end

  @doc """
  Updates a payout_transactions.

  ## Examples

      iex> update_payout_transaction(payout_transactions, %{field: new_value})
      {:ok, %PayoutTransactions{}}

      iex> update_payout_transaction(payout_transactions, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_payout_transaction(%PayoutTransactions{} = payout_transactions, attrs) do
    update_attrs = create_payout_update_attrs(attrs)

    result =
      Multi.new()
      |> Multi.update(
        :transaction,
        PayoutTransactions.changeset(payout_transactions, update_attrs)
      )
      |> Multi.insert(:history, fn %{transaction: transaction} ->
        TransactionHistory.changeset(%TransactionHistory{}, %{
          transaction_id: transaction.id,
          state: Atom.to_string(transaction.status),
          psp_result: attrs
        })
      end)
      |> Repo.transaction()

    case result do
      {:ok, %{transaction: payout_transaction, history: _transaction_history}} ->
        {:ok, %{transaction: payout_transaction}}

      {:error, failed_operation, failed_value, _changes_so_far} ->
        Logger.error(
          "Failed to create payout transaction in step #{inspect(failed_operation)} and error #{inspect(failed_value)}"
        )

        {:error, failed_value}
    end
  end

  @spec update_failed_payout_transaction(PayoutTransactions.t(), map()) ::
          {:ok, PayoutTransactions.t()} | {:error, atom}
  def update_failed_payout_transaction(%PayoutTransactions{id: id} = transaction, %{"status" => "FAILED"} = attrs) do
    with {_, %PayoutInvoice{} = invoice} <- {:get_invoice, get_payout_invoice(id, [:payout_invoice_lines])},
         update_attrs = create_payout_update_attrs(attrs),
         {_, {:ok, %{update_transaction: payout_transaction}}} <-
           {:update_transaction, do_update_failed_payout_transaction(transaction, update_attrs, invoice)} do
      {:ok, %{transaction: payout_transaction}}
    else
      {:get_invoice, nil} ->
        Logger.error("Failed to find payout invoice with id #{id}")
        {:error, :payout_invoice_not_found}

      {:update_transaction, {:error, failed_operation, failed_value, _changes_so_far}} ->
        Logger.error(
          "Failed to create payout transaction in step #{inspect(failed_operation)} and error #{inspect(failed_value)}"
        )

        {:error, failed_value}
    end
  end

  def create_payout_update_attrs(%{"id" => psp_reference, "status" => status} = attrs),
    do: %{
      "psp_reference" => psp_reference,
      "status" => status |> String.upcase() |> String.to_atom(),
      "psp_result" => attrs
    }

  def create_payout_update_attrs(attrs), do: attrs

  @doc """
  Deletes a payout_transactions.

  ## Examples

      iex> delete_payout_transactions(payout_transactions)
      {:ok, %PayoutTransactions{}}

      iex> delete_payout_transactions(payout_transactions)
      {:error, %Ecto.Changeset{}}

  """
  def delete_payout_transactions(%PayoutTransactions{} = payout_transactions) do
    Repo.delete(payout_transactions)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking payout_transactions changes.

  ## Examples

      iex> change_payout_transactions(payout_transactions)
      %Ecto.Changeset{data: %PayoutTransactions{}}

  """
  def change_payout_transactions(%PayoutTransactions{} = payout_transactions, attrs \\ %{}) do
    PayoutTransactions.changeset(payout_transactions, attrs)
  end

  def get_transfer_instruments(event_id) do
    with {_,
          {:ok,
           %Event{
             promoter: %Promoter{legal_entity_id: legal_entity_id, id: promoter_id},
             balance_account_id: balance_account_id
           }}} <- {:get_event_by_id, Events.get_payout_event(event_id)},
         {_, {:ok, balance_account}} <-
           {:get_balance_account, BalancePlatform.get_balance_account(balance_account_id)},
         {_, {:ok, legal_entity}} <-
           {:get_legal_entity, LegalEntityManagement.get_legal_entity(legal_entity_id)} do
      {:ok,
       %{
         "promoter" => %{"id" => promoter_id},
         "transfer_instruments" => extract_payment_instrument_from_legal_entity(legal_entity),
         "balance_account" => balance_account
       }}
    else
      {:get_event_by_id, {:error, error}} ->
        Logger.error("Failed to get event with id #{event_id} with reason #{inspect(error)}")
        {:error, error}

      {:get_balance_account, {:error, error}} ->
        Logger.error("Failed to get promoter balance account  with reason #{inspect(error)}")
        {:error, error}

      {:get_legal_entity, {:error, error}} ->
        Logger.error("Failed to get legal entity  with reason #{inspect(error)}")
        {:error, error}

      {step, error} ->
        Logger.error("Failed in step #{step} with reason #{inspect(error)}")
        error
    end
  end

  @doc """
      webhook with status received and authorised and type  balancePlatform.transfer.created are not processed by the system because
      we receive the same payload from adyen synchronously directly after we call the transfer endpoint
      and we don't want to process the same transaction twice.
  """
  def finalize_pay_out_transaction(
        %{
          "data" => %{"reference" => _reference, "status" => "received", "type" => "bankTransfer"} = _params,
          "type" => "balancePlatform.transfer.created"
        } = _webhook_data
      ) do
    :ok
  end

  def finalize_pay_out_transaction(
        %{
          "data" => %{"reference" => _reference, "status" => "authorised", "type" => "bankTransfer"} = _params,
          "type" => "balancePlatform.transfer.updated"
        } = _webhook_data
      ) do
    :ok
  end

  def finalize_pay_out_transaction(
        %{
          "data" => %{"reference" => reference, "status" => _status, "type" => "bankTransfer"} = params,
          "type" => "balancePlatform.transfer.updated"
        } = _webhook_data
      ) do
    with {_, payout_transaction} when not is_nil(payout_transaction) <-
           {:payout_transaction, get_payout_transaction(reference)},
         {_, :ok} <-
           {:check_transaction_state, check_transaction_state_allows_finalization(payout_transaction)},
         {_, {:ok, _updated_payout_transaction}} <-
           {:update_state_finalizing, update_payout_transaction(payout_transaction, %{status: :FINALIZING})},
         {_, {:ok, _updated_payout_transaction}} <-
           {:finalize_transaction, update_payout_transaction(payout_transaction, params)},
         {_, :ok} <-
           {:maybe_publish_payout_invoice_pdf, maybe_publish_payout_invoice_pdf(payout_transaction)} do
      Logger.info("Successfully finalized payout transaction with reference #{inspect(reference)}")

      :ok
    else
      {:payout_transaction, nil} ->
        Logger.error("Failed to find payout transaction with reference #{inspect(reference)}")
        {:error, :payout_transaction_not_found}

      {:check_transaction_state, {:error, error}} ->
        Logger.error("Failed to check transaction state with error #{inspect(error)}")
        {:error, :transaction_state_not_allowed_for_finalization}

      {:update_state_finalizing, {:error, error}} ->
        Logger.error("Failed to update state to finalizing with error #{inspect(error)}")
        {:error, :failed_to_update_state_to_finalizing}

      {:finalize_transaction, {:error, error}} ->
        Logger.error("Failed to finalize payout transaction with error #{inspect(error)}")
        {:error, :failed_to_finalize_transaction}

      {:maybe_publish_payout_invoice_pdf, {:error, error}} ->
        Logger.error("Failed to publish payout invoice pdf with error #{inspect(error)}")
        {:error, :failed_to_publish_payout_invoice_pdf}

      error ->
        Logger.error("Failed to finalize payout transaction with error #{inspect(error)}")
        {:error, :failed_to_finalize_transaction}
    end
  end

  def finalize_pay_out_transaction(_webhook_data), do: :ok

  @spec prepare_data_for_payout_invoice_generation(payout :: PayoutTransactions.t()) :: {:ok, map()} | {:error, atom}
  def prepare_data_for_payout_invoice_generation(
        %PayoutTransactions{id: id, event_id: event_id, created_by: initiated_by} = transaction
      ) do
    with {_, %PayoutInvoice{} = invoice} <- {:get_payout_invoice, get_payout_invoice(id, [:payout_invoice_lines])},
         {_, %Promoter{created_by_document_id: created_by_document_id} = promoter} <-
           {:get_promoter, Vendor.get_promoter_by_event_id(event_id, address: :country)},
         {_, %Event{venue: venue} = event} <-
           {:get_event, Events.get_event(event_id, venue: [address: :country])},
         {_, {:ok, initiated_by_user}} <- {:get_initiator, AccountsService.get_user_by_id(initiated_by)},
         {_, {:ok, promoter_user}} <- {:get_promoter_user, AccountsService.get_user_by_id(created_by_document_id)} do
      {:ok,
       %{
         email: prepare_email_data(promoter_user, initiated_by_user, promoter),
         invoice: prepare_invoice_data(transaction, invoice),
         promoter: prepare_promoter_data(promoter),
         event: prepare_event_data(event),
         venue: prepare_venue_data(venue)
       }}
    else
      {:get_payout_invoice, nil} ->
        Logger.error("Failed to find payout invoice with id #{id}")
        {:error, :payout_invoice_not_found}

      {:get_promoter, nil} ->
        Logger.error("Failed to find promoter for event with id #{event_id}")
        {:error, :promoter_not_found}

      {:get_event, nil} ->
        Logger.error("Failed to find event with id #{event_id}")
        {:error, :event_not_found}

      {:get_initiator, {:error, error}} ->
        Logger.error("Failed to get initiator with error #{inspect(error)}")
        {:error, :initiator_not_found}

      {:get_promoter_user, {:error, error}} ->
        Logger.error("Failed to get promoter user with error #{inspect(error)}")
        {:error, :promoter_user_not_found}
    end
  end

  @spec get_payout_invoice_by_number(invoice_number :: String.t()) :: PayoutInvoice.t() | nil
  def get_payout_invoice_by_number(invoice_number) do
    Repo.one(from(pi in PayoutInvoice, where: pi.invoice_number == ^invoice_number and is_nil(pi.deleted_at)))
  end

  @spec update_invoice(invoice :: PayoutInvoice.t(), map()) :: {:ok, PayoutInvoice.t()} | {:error, any()}
  def update_invoice(%PayoutInvoice{} = invoice, data) do
    invoice
    |> PayoutInvoice.changeset(data)
    |> Repo.update()
  end

  @spec get_payout_invoice(id :: Ecto.UUID.t(), preloads :: list(atom())) :: PayoutInvoice.t() | nil
  def get_payout_invoice(id, preloads \\ []) do
    Logger.debug("Fetching payout invoice with id #{id}")

    Repo.one(
      from(pi in PayoutInvoice, where: pi.payout_transaction_id == ^id and is_nil(pi.deleted_at), preload: ^preloads)
    )
  end

  defp calculate_gross_tax_and_net_amount(tax_rate, gross_amount_cent) do
    net_amount_cent = round(gross_amount_cent / (1 + tax_rate))
    tax_amount_cent = gross_amount_cent - net_amount_cent
    gross_amount = Money.to_euro_string(gross_amount_cent)
    tax_amount = Money.to_euro_string(tax_amount_cent)
    net_amount = Money.to_euro_string(net_amount_cent)

    %{gross_amount: gross_amount, tax_amount: tax_amount, net_amount: net_amount}
  end

  defp check_transaction_state_allows_finalization(%{status: state}) when state in [:INITIATED, :AUTHORISED], do: :ok

  defp check_transaction_state_allows_finalization(%{status: :FINALIZING}) do
    Logger.warning("Transaction already being finalized cannot be finalized again")
    {:error, :transaction_already_processed}
  end

  defp check_transaction_state_allows_finalization(%{status: :BOOKED}) do
    Logger.error("Transaction already processed cannot be finalized")
    {:error, :transaction_already_processed}
  end

  defp check_transaction_state_allows_finalization(%{status: state}) do
    Logger.error("Transaction state #{inspect(state)} not allowed for finalization")
    {:error, :transaction_state_not_allowed_for_finalization}
  end

  defp extract_payment_instrument_from_legal_entity(%{"problems" => [_problem | _]}), do: []

  defp extract_payment_instrument_from_legal_entity(%{"transferInstruments" => transfer_instruments}),
    do: transfer_instruments

  defp extract_payment_instrument_from_legal_entity(_param), do: []

  defp get_next_promoter_serial(multi, promoter_id) do
    multi
    |> Multi.run(:read_lock_serial, fn repo, _changes ->
      query =
        from(p in PayoutInvoice,
          where: p.promoter_id == ^promoter_id and is_nil(p.deleted_at),
          lock: "FOR UPDATE"
        )

      case repo.all(query) do
        nil -> {:error, :not_found}
        invoice -> {:ok, invoice}
      end
    end)
    |> Multi.run(:get_max_serial, fn repo, _changes ->
      query =
        from(p in PayoutInvoice,
          where: p.promoter_id == ^promoter_id and is_nil(p.deleted_at),
          select: max(p.serial)
        )

      case repo.one(query) do
        nil -> {:ok, 1}
        serial -> {:ok, serial + 1}
      end
    end)
  end

  # is_small_business false and  payout_invoice_enabled true then  invoice is created
  defp do_create_pre_init_payout_transaction(
         %{"promoter_id" => promoter_id} = attrs,
         total_ticket_sold,
         short_code,
         total_ticket_tax_rate_based,
         false,
         true
       ) do
    Multi.new()
    |> Multi.insert(
      :insert_transaction,
      PayoutTransactions.pre_init_changeset(%PayoutTransactions{}, attrs)
    )
    |> Multi.insert(:insert_transaction_history, fn %{insert_transaction: transaction} ->
      TransactionHistory.changeset(%TransactionHistory{}, %{
        transaction_id: transaction.id,
        state: Atom.to_string(transaction.status),
        psp_result: %{}
      })
    end)
    |> get_next_promoter_serial(promoter_id)
    |> Multi.insert(:insert_invoice, fn %{insert_transaction: transaction, get_max_serial: promoter_next_serial} ->
      string_serial = PayoutInvoice.string_serial(promoter_next_serial)

      invoice_number = "A-#{DateTime.utc_now().year}-#{short_code}-#{string_serial}"

      PayoutInvoice.changeset(%PayoutInvoice{}, %{
        invoice_number: invoice_number,
        payout_transaction_id: transaction.id,
        promoter_id: promoter_id,
        total_ticket_sold: total_ticket_sold,
        serial: promoter_next_serial
      })
    end)
    |> Multi.insert_all(:insert_invoice_lines, PayoutInvoiceLine, fn %{insert_invoice: %{id: invoice_id}} ->
      now = DateTime.utc_now()

      Enum.reduce(total_ticket_tax_rate_based, [], fn
        %{tax_rate: _tax_rate, total: 0}, acc ->
          acc

        %{tax_rate: tax_rate, total: total}, acc ->
          List.flatten([
            %{
              tax_rate: tax_rate,
              total_ticket: total,
              payout_invoice_id: invoice_id,
              inserted_at: DateTime.truncate(now, :second),
              updated_at: DateTime.truncate(now, :second)
            }
            | acc
          ])
      end)
    end)
    |> Repo.transaction()
  end

  # can be small business but payout_invoice_enabled false  or is_small_business true still payout_invoice_enabled false
  #  then no invoice is created
  defp do_create_pre_init_payout_transaction(
         %{"promoter_id" => _promoter_id} = attrs,
         _total_ticket_sold,
         _short_code,
         _total_ticket_tax_rate_based,
         _is_small_business,
         _payout_invoice_feature_enabled
       ) do
    Multi.new()
    |> Multi.insert(
      :insert_transaction,
      PayoutTransactions.pre_init_changeset(%PayoutTransactions{}, attrs)
    )
    |> Multi.insert(:insert_transaction_history, fn %{insert_transaction: transaction} ->
      TransactionHistory.changeset(%TransactionHistory{}, %{
        transaction_id: transaction.id,
        state: Atom.to_string(transaction.status),
        psp_result: %{}
      })
    end)
    |> Repo.transaction()
  end

  defp do_update_failed_payout_transaction(%PayoutTransactions{} = payout_transactions, update_attrs, invoice) do
    Multi.new()
    |> Multi.update(
      :update_transaction,
      PayoutTransactions.changeset(payout_transactions, update_attrs)
    )
    |> Multi.insert(:insert_history, fn %{update_transaction: transaction} ->
      TransactionHistory.changeset(%TransactionHistory{}, %{
        transaction_id: transaction.id,
        state: Atom.to_string(transaction.status),
        psp_result: update_attrs["psp_result"]
      })
    end)
    |> Multi.update(:update_invoice, fn _changes ->
      PayoutInvoice.changeset(invoice, %{"deleted_at" => DateTime.utc_now()})
    end)
    |> Multi.update_all(
      :update_invoice_lines,
      fn _changes ->
        now = DateTime.utc_now()

        from(pil in PayoutInvoiceLine,
          where: pil.payout_invoice_id == ^invoice.id,
          update: [set: [deleted_at: ^now]]
        )
      end,
      []
    )
    |> Repo.transaction()
  end

  def maybe_publish_payout_invoice_pdf(%PayoutTransactions{event_id: event_id} = payout_transaction) do
    with {_, %Promoter{is_small_business: false, id: promoter_id} = promoter} <-
           {:get_promoter, Vendor.get_promoter_by_event_id(event_id, [:address])},
         unleash_payload = %{promoter_id: promoter_id, event_id: event_id},
         {_, true} <- {:payout_enabled, Unleash.enabled?(:enable_payout_invoice_generation, unleash_payload)},
         {_, true} <- {:event_promoter_is_from_germany, event_promoter_is_from_germany?(promoter)},
         {_, true} <- {:event_venue_in_germany, event_venue_in_germany?(event_id)},
         {_, true} <- {:event_has_valid_tax_rate, event_has_only_valid_tax_rate?(event_id)},
         {_, {:ok, invoice_data}} <-
           {:prepare_data_for_payout_invoice, prepare_data_for_payout_invoice_generation(payout_transaction)} do
      PayoutInvoicePublisher.publish_payout_invoice_pdf_generation(invoice_data)
      :ok
    else
      {:get_promoter, nil} ->
        Logger.error("Failed to get promoter for event #{event_id}")
        {:error, :promoter_not_found}

      {:get_promoter, %Promoter{is_small_business: true}} ->
        Logger.info("Promoter for event #{event_id} is a small business, payout invoice will not be generated")
        :ok

      {:payout_enabled, false} ->
        Logger.info("Automated payout invoice sending is not enabled for event: #{event_id}")
        :ok

      {:event_promoter_is_from_germany, false} ->
        Logger.info("Event promoter for event #{event_id} is not from Germany, payout invoice will not be generated")
        :ok

      {:event_venue_in_germany, false} ->
        Logger.info("Event venue for event #{event_id} is not in Germany, payout invoice will not be generated")
        :ok

      {:event_has_valid_tax_rate, false} ->
        Logger.info("Event #{event_id} has invalid tax rate, payout invoice will not be generated")
        :ok

      {:prepare_data_for_payout_invoice, {:error, error}} ->
        Logger.error(
          "Failed to prepare data for payout invoice publication for event #{event_id} with error #{inspect(error)}"
        )

        {:error, :failed_to_prepare_data_for_payout_invoice}
    end
  end

  defp prepare_invoice_data(%PayoutTransactions{amount: amount} = payout_transaction, %PayoutInvoice{
         invoice_number: invoice_number,
         total_ticket_sold: total_ticket_sold,
         payout_invoice_lines: payout_invoice_lines
       }) do
    invoices_positions =
      Enum.map(payout_invoice_lines, fn %{tax_rate: tax_rate, total_ticket: line_total_tickets} ->
        line_amount = round(amount / total_ticket_sold * line_total_tickets)

        %{gross_amount: gross_amount, tax_amount: tax_amount, net_amount: net_amount} =
          calculate_gross_tax_and_net_amount(tax_rate, line_amount)

        %{
          taxRate: Money.to_percentage_string(tax_rate),
          grossAmount: gross_amount,
          taxAmount: tax_amount,
          netAmount: net_amount
        }
      end)

    %{
      invoiceNumber: invoice_number,
      invoiceDate: %{
        date: DateTimeUtil.to_default_string(payout_transaction.inserted_at, :date),
        time: DateTimeUtil.to_default_string(payout_transaction.inserted_at, :time)
      },
      grossTotal: Money.to_euro_string(amount),
      accountIdentifier: payout_transaction.transfer_instrument,
      positions: invoices_positions
    }
  end

  defp prepare_event_data(%Event{id: event_id, title: title, start_date: start_date} = _event) do
    %{
      id: event_id,
      title: title,
      startDate: %{
        date: DateTimeUtil.to_default_string(start_date, :date),
        time: DateTimeUtil.to_default_string(start_date, :time)
      }
    }
  end

  defp prepare_venue_data(%Venue{address: %Address{} = address} = venue) do
    %{
      name: venue.name,
      streetAddress: address.street_address,
      postalCode: address.postal_code,
      locality: address.locality,
      country: address.country.country
    }
  end

  defp prepare_promoter_data(%Promoter{address: %Address{} = address} = promoter) do
    %{
      address: %{
        streetAddress: address.street_address,
        postalCode: address.postal_code,
        locality: address.locality,
        country: address.country.country
      },
      companyName: promoter.company_name || promoter.display_name || "",
      displayName: promoter.display_name,
      fullName: "#{promoter.given_name} #{promoter.family_name}",
      balanceAccountId: promoter.balance_account_id,
      accountHolderId: promoter.account_holder_id,
      vatId: promoter.vat_id,
      taxId: promoter.tax_id
    }
  end

  defp prepare_email_data(%{"email" => promoter_email}, %{"email" => promoter_email}, promoter) do
    %{
      from: "<EMAIL>",
      to: promoter_email,
      bcc: "<EMAIL>",
      name: get_promoter_name(promoter)
    }
  end

  defp prepare_email_data(%{"email" => promoter_email}, %{"email" => initiator_email}, promoter) do
    %{
      from: "<EMAIL>",
      to: promoter_email,
      cc: initiator_email,
      bcc: "<EMAIL>",
      name: get_promoter_name(promoter)
    }
  end

  defp get_promoter_name(%Promoter{given_name: given_name}) when not is_nil(given_name), do: given_name
  defp get_promoter_name(%Promoter{display_name: display_name}) when not is_nil(display_name), do: display_name
  defp get_promoter_name(%Promoter{company_name: company_name}) when not is_nil(company_name), do: company_name
  defp get_promoter_name(_), do: ""
end
