defmodule EventsServiceWeb.VoucherController do
  use EventsServiceWeb, :controller
  use OpenApiSpex.ControllerSpecs
  use Params

  alias EventsService.Promotion
  alias EventsService.Promotion.Voucher
  alias EventsServiceWeb.ApiSchemas.VoucherSchema
  alias EventsServiceWeb.ApiSchemas.VoucherSchema.VoucherRequest
  alias EventsServiceWeb.ApiSchemas.VoucherSchema.VoucherResponse
  alias EventsServiceWeb.ApiSchemas.VoucherSchema.VouchersResponse
  alias EventsServiceWeb.ChangesetJSON
  alias EventsServiceWeb.Plugs.Authorize
  alias OpenApiSpex.Reference

  require Logger

  action_fallback EventsServiceWeb.FallbackController

  plug Authorize,
       [rule: ["promoter", "event", "write"], permission: "event.edit"] when action in [:create, :update, :delete]

  tags ["Vouchers"]
  security [%{"bearerAuth" => []}]

  operation :index,
    summary: "List vouchers",
    parameters: VoucherSchema.voucher_request_params(),
    responses: %{
      :ok => {"Get vouchers response", "application/json", VouchersResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :unauthorized => %Reference{"$ref": "#/components/responses/unauthorized"},
      :forbidden => %Reference{"$ref": "#/components/responses/forbidden"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"}
    }

  # No API documenation for internal service calls
  def index(conn, %{"code" => code, "scope" => "service"} = _params) do
    with [api_token | _] <- get_req_header(conn, "x-api-token"),
         {:ok, _claim} <- ExServiceClient.Token.verify_and_validate(api_token) do
      vouchers = Promotion.get_available_vouchers_by_code(code, [:voucher_counter])

      conn
      |> put_status(:ok)
      |> render(:service_index, vouchers: vouchers)
    else
      [] ->
        Logger.error("Service authentication failed because of missing token")

        conn
        |> put_status(:forbidden)
        |> json(%{error: "Missing token"})

      {:error, msg} ->
        Logger.error("Service authentication failed because of #{inspect(msg)}")

        conn
        |> put_status(:forbidden)
        |> json(%{error: msg})
    end
  end

  # No API documentation for deprecated API, it's not paginated but the response type is the same
  # deprecated
  def index(%{assigns: %{current_user_id: _user_id}} = conn, %{"event_id" => event_id} = _params) do
    if event_access?(conn, event_id, "event.edit") do
      case Promotion.fetch_vouchers_for_event(event_id) do
        {:ok, vouchers} ->
          conn
          |> put_status(:ok)
          |> render(:index, vouchers: vouchers)

        {:error, :event_not_found} ->
          conn
          |> put_status(:not_found)
          |> json(%{message: "Event not found", error_code: :event_not_found})
      end
    else
      conn
      |> put_status(:forbidden)
      |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})
    end
  end

  def index(
        %{assigns: %{current_user_id: _user_id}} = conn,
        %{"scope_id" => scope_id, "voucher_scope" => scope} = params
      ) do
    scope = String.to_existing_atom(scope)

    if voucher_access?(conn, scope, scope_id) do
      vouchers_page =
        Promotion.list_vouchers(
          %{scope: scope, scope_id: scope_id},
          params,
          [:voucher_counter]
        )

      conn
      |> put_status(:ok)
      |> render(:index, page: vouchers_page)
    else
      conn
      |> put_status(:forbidden)
      |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})
    end
  end

  operation :create,
    summary: "Create a new voucher for an organizer, event, variant or ticket category",
    security: [%{"bearerAuth" => []}],
    request_body: {"Voucher params", "application/json", VoucherRequest},
    responses: %{
      :ok => {"Get voucher response", "application/json", VoucherResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :unauthorized => %Reference{"$ref": "#/components/responses/unauthorized"},
      :forbidden => %Reference{"$ref": "#/components/responses/forbidden"},
      :unprocessable_entity => %Reference{"$ref": "#/components/responses/unprocessable_entity"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/internal_server_error"}
    }

  # deprecated
  def create(%{assigns: %{current_user_id: _user_id}} = conn, %{"event_id" => event_id} = voucher_params) do
    with {_, true} <- {:access_validation, event_access?(conn, event_id, "event.edit")},
         # currently organizer are only allowed to create event voucher
         # Add permission check to the scope before remove!
         voucher_params = prepare_deprecated_voucher_params(voucher_params),
         {_, {:ok, %Voucher{id: id} = _voucher}} <- {:create_voucher, Promotion.create_voucher(voucher_params)},
         # Read voucher from the database again to preload the voucher_counter
         # get only event_voucher to keep the API backwarts compatible
         {_, %Voucher{scope_id: _event_id, scope: :EVENT} = voucher} <-
           {:get_voucher, Promotion.get_voucher_by_id(id)} do
      user_agent = conn |> Plug.Conn.get_req_header("user-agent") |> List.first()

      Logger.warning("Deprecated API #{inspect(conn.request_path)} called with user-agent #{inspect(user_agent)}")

      conn
      |> put_status(:ok)
      |> put_resp_header("x-api-deprecated", "true")
      |> render(:show, voucher: voucher)
    else
      {:access_validation, false} ->
        conn
        |> put_status(:forbidden)
        |> put_resp_header("x-api-deprecated", "true")
        |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})

      {:create_voucher, {:error, error}} ->
        Logger.error("Failed to create voucher: #{ChangesetJSON.error_to_text(error)}")

        conn
        |> put_status(:unprocessable_entity)
        |> put_resp_header("x-api-deprecated", "true")
        |> json(%{
          message: "Failed to create voucher with error: #{ChangesetJSON.error_to_text(error)}",
          error_code: :create_voucher_error
        })

      {:get_voucher, _} ->
        Logger.critical("Failed to get voucher after creation")

        conn
        |> put_status(:internal_server_error)
        |> put_resp_header("x-api-deprecated", "true")
        |> json(%{message: "Voucher not found", error_code: :internal_server_error})
    end
  end

  defparams(
    create_params(
      # styler:sort
      %{
        code: [field: :string, default: nil],
        description: [field: :string, default: nil],
        limit_per_redemption: [field: :integer, default: 1],
        min_items: [field: :integer, default: -1],
        quota: [field: :integer, default: 0],
        scope: [field: Ecto.Enum, values: [:EVENT, :ORGANIZER, :TICKET_CATEGORY, :VARIANT]],
        scope_id: [field: Ecto.UUID],
        valid_from: [field: :utc_datetime, default: nil],
        valid_until: [field: :utc_datetime, default: nil],
        value: [field: :integer, default: 0]
      }
    )
  )

  def create(
        %{assigns: %{current_user_id: _user_id}} = conn,
        %{"scope_id" => scope_id, "scope" => scope} = voucher_params
      ) do
    with {_, %{valid?: true}} <- {:params_validation, create_params(voucher_params)},
         {_, true} <- {:access_validation, voucher_access?(conn, String.to_existing_atom(scope), scope_id)},
         {_, {:ok, %Voucher{id: id} = _voucher}} <- {:create_voucher, Promotion.create_voucher(voucher_params)},
         {_, %Voucher{} = voucher} <-
           {:get_voucher, Promotion.get_voucher_by_id(id)} do
      conn
      |> put_status(:ok)
      |> render(:show, voucher: voucher)
    else
      {:params_validation, {:error, error}} ->
        Logger.error("Failed to validate voucher parameters: #{ChangesetJSON.error_to_text(error)}")

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          message: "Invalid parameters: #{ChangesetJSON.error_to_text(error)}",
          error_code: :invalid_parameter
        })

      {:access_validation, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})

      {:create_voucher, {:error, error}} ->
        Logger.error("Failed to create voucher: #{ChangesetJSON.error_to_text(error)}")

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          message: "Failed to create voucher with error: #{ChangesetJSON.error_to_text(error)}",
          error_code: :create_voucher_error
        })

      {:get_voucher, _} ->
        Logger.critical("Failed to get voucher after creation")

        conn
        |> put_status(:internal_server_error)
        |> json(%{message: "Voucher not found", error_code: :internal_server_error})
    end
  end

  def create(%{assigns: %{current_user_id: user_id}} = conn, params) do
    Logger.error("Invalid request to create voucher: #{inspect(params)} from user #{user_id}")

    conn
    |> put_status(:unprocessable_entity)
    |> json(%{message: "Invalid parameters", error_code: :invalid_parameter})
  end

  def show(conn, %{"id" => id}) do
    voucher =
      case Ecto.UUID.cast(id) do
        {:ok, _uuid} ->
          Promotion.get_voucher_by_id(id)

        :error ->
          Promotion.get_voucher_by_code(id)
      end

    case voucher do
      nil -> conn |> put_status(:not_found) |> json(%{message: "Not found", error_code: :not_found})
      voucher -> render(conn, :show, voucher: voucher)
    end
  end

  def update(%{assigns: %{current_user_id: _user_id}} = conn, %{"id" => id} = voucher_params) do
    with {_, %Voucher{scope_id: scope_id, scope: scope} = voucher} <-
           {:get_voucher, Promotion.get_voucher_by_id(id)},
         {_, true} <-
           {:access_validation, voucher_access?(conn, scope, scope_id)},
         {_, {:ok, %Voucher{} = _voucher}} <- {:update_voucher, Promotion.update_voucher(voucher, voucher_params)} do
      conn |> put_status(:ok) |> json(%{message: "Success"})
    else
      {:get_voucher, nil} ->
        conn |> put_status(:not_found) |> json(%{message: "Voucher not found", error_code: :not_found})

      {:get_voucher, _voucher} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})

      {:access_validation, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})

      {:update_voucher, {:error, error}} ->
        Logger.error("Failed to update voucher: #{ChangesetJSON.error_to_text(error)}")

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          message: "Failed to update voucher with error: #{ChangesetJSON.error_to_text(error)}",
          error_code: :update_voucher_error
        })
    end
  end

  def delete(conn, %{"id" => id}) do
    with {_, %Voucher{scope_id: scope_id, scope: scope} = voucher}
         when scope in [:VARIANT, :TICKET_CATEGORY, :EVENT, :ORGANIZER] <-
           {:get_voucher, Promotion.get_voucher_by_id(id)},
         {_, true} <-
           {:access_validation, voucher_access?(conn, scope, scope_id)},
         {_, {:ok, %Voucher{}}} <- {:delete_voucher, Promotion.delete_voucher(voucher)} do
      conn |> put_status(:ok) |> json(%{message: "Success"})
    else
      {:get_voucher, nil} ->
        conn |> put_status(:not_found) |> json(%{message: "Voucher not found", error_code: :not_found})

      {:get_voucher, _voucher} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})

      {:access_validation, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})

      {:delete_voucher, {:error, msg}} when is_binary(msg) ->
        Logger.error("Failed to delete voucher: #{msg}")

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          message: "Failed to delete voucher.",
          error_code: :delete_voucher_error
        })

      {:delete_voucher, {:error, error}} ->
        Logger.error("Failed to delete voucher: #{ChangesetJSON.error_to_text(error)}")

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          message: "Failed to delete voucher.",
          error_code: :delete_voucher_error
        })
    end
  end

  defp prepare_deprecated_voucher_params(voucher_params) do
    voucher_params = voucher_params |> Map.delete("scope_id") |> Map.delete("scope") |> Map.put("valid_until", nil)

    case voucher_params["isActive"] do
      true -> Map.put(voucher_params, "valid_from", DateTime.utc_now())
      "true" -> Map.put(voucher_params, "valid_from", DateTime.utc_now())
      _ -> Map.put(voucher_params, "valid_from", nil)
    end
  end

  defp voucher_access?(conn, :ORGANIZER, scope_id), do: organizer_access?(conn, scope_id)
  defp voucher_access?(conn, "ORGANIZER", scope_id), do: organizer_access?(conn, scope_id)
  defp voucher_access?(conn, scope, scope_id), do: event_access?(conn, scope_id, "event.edit", scope)
end
