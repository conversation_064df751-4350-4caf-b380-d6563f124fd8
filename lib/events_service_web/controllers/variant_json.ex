defmodule EventsServiceWeb.VariantJSON do
  @moduledoc false

  alias EventsService.Events.Variant
  alias EventsService.MLP.MultiLevelPricingModifier
  alias EventsService.VariantQuota
  alias EventsServiceWeb.AvailabilityJSON
  alias EventsServiceWeb.MultiLevelPricingModifierJSON
  alias EventsServiceWeb.SalesChannelJSON
  alias EventsServiceWeb.TicketCategoryJSON

  def organizer_index(%{page: page}) do
    # styler:sort
    %{
      data: for(variant <- page.entries, do: organizer_data(variant)),
      pageNumber: page.page_number,
      pageSize: page.page_size,
      totalEntries: page.total_entries,
      totalPages: page.total_pages
    }
  end

  def public_index(%{page: page}) do
    # styler:sort
    %{
      data: for(variant <- page.entries, do: public_data(variant)),
      pageNumber: page.page_number,
      pageSize: page.page_size,
      totalEntries: page.total_entries,
      totalPages: page.total_pages
    }
  end

  def sales_channel_index(%{variants: variants}) do
    for(variant <- variants, do: sales_channel_data(variant))
  end

  def mlpm_index(%{variants: variants}) do
    for(variant <- variants, do: mlpm_data(variant))
  end

  def organizer_show(%{variant: variant}) do
    organizer_data(variant)
  end

  defp organizer_data(%Variant{availability: availability, ticket_category: ticket_category} = variant) do
    sold =
      case variant do
        %{variant_counter: %{sold: sold}} -> sold
        _ -> nil
      end

    # styler:sort
    %{
      availability: AvailabilityJSON.organizer_show(%{availability: availability}),
      distributionType: variant.distribution_type,
      eventId: variant.event_id,
      id: variant.id,
      insertedAt: variant.inserted_at,
      isSoldOutFromReservation: variant.is_sold_out_from_reservation,
      isVisible: variant.is_visible,
      maxAmount: variant.max_amount,
      minAmount: variant.min_amount,
      multiLevelPricingModifier: get_mlpm_data(variant.mlpm),
      orderNo: variant.order_no,
      predecessorId: variant.predecessor_id,
      presaleStartDate: variant.presale_start_date,
      quota: VariantQuota.get_variant_quota(variant),
      salesChannel: SalesChannelJSON.base_data(variant.sales_channel),
      sold: sold,
      status: variant.status,
      successors: variant.successors,
      ticketCategory: TicketCategoryJSON.organizer_show(%{ticket_category: ticket_category}),
      unitPrice: variant.unit_price,
      visibilityAfterSalesEnded: variant.visibility_after_sales_ended,
      visibilityBeforeSalesStarted: variant.visibility_before_sales_started
    }
  end

  defp public_data(%Variant{availability: availability, ticket_category: ticket_category} = variant) do
    remaining_quota = VariantQuota.get_public_remaining_quota(variant)
    # styler:sort
    %{
      availability:
        AvailabilityJSON.public_show(%{
          availability: availability,
          visibility_after_sales_ended: variant.visibility_after_sales_ended,
          visibility_before_sales_started: variant.visibility_before_sales_started
        }),
      eventId: variant.event_id,
      grossPrice: variant.gross_price,
      id: variant.id,
      maxAmount: variant.max_amount,
      minAmount: variant.min_amount,
      multiLevelPricingModifier: get_public_mlpm_data(variant.mlpm),
      remainingQuota: remaining_quota,
      status: variant.status,
      ticketCategory:
        TicketCategoryJSON.public_show(%{ticket_category: ticket_category, remaining_quota: remaining_quota})
    }
  end

  defp sales_channel_data(%Variant{ticket_category: ticket_category, status: status} = variant) do
    remaining_quota = VariantQuota.get_remaining_quota(variant)
    # styler:sort
    %{
      eventId: variant.event_id,
      grossPrice: variant.gross_price,
      id: variant.id,
      originalPrice: get_sales_channel_original_price(variant),
      remainingQuota: remaining_quota,
      status: status,
      ticketCategory:
        TicketCategoryJSON.public_show(%{ticket_category: ticket_category, remaining_quota: remaining_quota})
    }
  end

  # As of now, a duplicate of sales_channel_data because MLPMs don't need much data from variants
  # and we don't want to use the sales_channel_data DTO to avoid confusion
  # https://github.com/stagedates/events-service/pull/630/files#r2004340584
  defp mlpm_data(%Variant{ticket_category: ticket_category, status: status} = variant) do
    remaining_quota = VariantQuota.get_remaining_quota(variant)
    # styler:sort
    %{
      eventId: variant.event_id,
      grossPrice: variant.gross_price,
      id: variant.id,
      remainingQuota: remaining_quota,
      status: status,
      ticketCategory:
        TicketCategoryJSON.public_show(%{ticket_category: ticket_category, remaining_quota: remaining_quota})
    }
  end

  # Currently we have max one MLPM for a variant, there is a unique contraint index on the DB in the helper table
  defp get_mlpm_data([]), do: nil
  defp get_mlpm_data([%MultiLevelPricingModifier{} = modifier | _]), do: get_mlpm_data(modifier)

  defp get_mlpm_data(%MultiLevelPricingModifier{} = modifier),
    do: MultiLevelPricingModifierJSON.show(%{modifier: modifier})

  defp get_mlpm_data(_mlpm), do: nil

  defp get_public_mlpm_data([]), do: nil
  defp get_public_mlpm_data([%MultiLevelPricingModifier{} = modifier | _]), do: get_public_mlpm_data(modifier)

  defp get_public_mlpm_data(%MultiLevelPricingModifier{} = modifier),
    do: MultiLevelPricingModifierJSON.public_show(%{modifier: modifier})

  defp get_public_mlpm_data(_mlpm), do: nil

  defp get_sales_channel_original_price(%{sales_channel: %{original_price: nil}}), do: nil
  defp get_sales_channel_original_price(%{sales_channel: %{original_price: original_price}}), do: original_price
  defp get_sales_channel_original_price(_variant), do: nil
end
