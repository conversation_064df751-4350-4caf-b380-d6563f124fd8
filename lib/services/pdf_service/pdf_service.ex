defmodule ExServiceClient.Services.PdfService do
  alias ExServiceClient.Services.PdfService.PdfRequests

  @spec get_hard_tickets_pdf(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_hard_tickets_pdf(params), to: PdfRequests

  @spec get_order_summary_pdf(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_order_summary_pdf(params), to: PdfRequests

  @spec get_event_summary_pdf(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_event_summary_pdf(params), to: PdfRequests
end