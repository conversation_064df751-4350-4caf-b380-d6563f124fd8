steps:

  - name: "gcr.io/cloud-builders/docker"
    id: "docker-pull"
    entrypoint: "bash"
    args:
      - "-c"
      - "docker pull europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:latest || exit 0"
    waitFor: ["-"]

  ## use the global $SHORT_SHA for a rollout rollback strategy
  - name: "gcr.io/cloud-builders/docker"
    id: "docker-build"
    args:
      [
        "build",
        "-t",
        "europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:latest",
        "-t",
        "europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:$SHORT_SHA",
        "--build-arg=_PRJENV=$_PRJENV",
        "--cache-from",
        "europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:latest",
        "-f",
        "infrastructure/Dockerfile",
        ".",
      ]

  - name: "gcr.io/cloud-builders/docker"
    id: "docker-push-sha"
    args:
      [
        "push",
        "europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:$SHORT_SHA",
      ]

  - name: "gcr.io/cloud-builders/docker"
    id: "docker-push-lastest"
    args:
      [
        "push",
        "europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:latest",
      ]

  - name: "gcr.io/cloud-builders/kubectl"
    id: "kubectl-get-credentials"
    entrypoint: "sh"
    args:
      - "-c"
      - 'gcloud container clusters get-credentials --region "$_REGION" "sd-a01"'
    waitFor: ["docker-push-lastest"]

  - name: "gcr.io/cloud-builders/kubectl"
    id: "kubectl-set-image"
    entrypoint: "sh"
    args:
      - "-c"
      - |
        kubectl --namespace=$_PRJENV set image deployment/shortlink shortlink=europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:$SHORT_SHA \
        && kubectl --namespace=$_PRJENV annotate deployment/shortlink kubernetes.io/change-cause="image updated to $SHORT_SHA"
    waitFor: ["kubectl-get-credentials", "docker-push-sha"]

  - name: "gcr.io/cloud-builders/kubectl"
    id: "kubectl-set-version"
    entrypoint: "sh"
    args:
      - "-c"
      - 'kubectl --namespace=$_PRJENV set env deployment/shortlink VERSION="$SHORT_SHA"'
    waitFor: ["kubectl-get-credentials", "kubectl-set-image"]

images:
  - "europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:$SHORT_SHA"

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Options                                                                    ║
# ╚════════════════════════════════════════════════════════════════════════════╝

options:
  machineType: "E2_HIGHCPU_8"
  logging: "CLOUD_LOGGING_ONLY"

timeout: "1600s"
