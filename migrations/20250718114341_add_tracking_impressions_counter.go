package migrations

import (
	"fmt"

	"gofr.dev/pkg/gofr/migration"
)

func AddTrackingImpressionsCounter() migration.Migrate {
	return migration.Migrate{
		UP: func(d migration.Datasource) error {

			fmt.Println("AddTrackingImpressionsCounter migration: Starting migration execution")

			queries := []string{
				`ALTER TABLE shortlink.link ADD COLUMN IF NOT EXISTS tracking_impressions_counter INTEGER DEFAULT 0;`,
			}

			for i, query := range queries {
				fmt.Printf("AddTrackingImpressionsCounter migration: Executing query %d: %s\n", i+1, query)
				_, err := d.SQL.Exec(query)
				if err != nil {
					fmt.Printf("AddTrackingImpressionsCounter migration: Error executing query %d: %v\n", i+1, err)
					return err
				}
				fmt.Printf("AddTrackingImpressionsCounter migration: Successfully executed query %d\n", i+1)
			}

			fmt.Printf("AddTrackingImpressionsCounter migration: Migration completed successfully")
			return nil

		},
	}
}
