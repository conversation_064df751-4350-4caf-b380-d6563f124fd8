package migrations

import (
	"gofr.dev/pkg/gofr/migration"
)

func RemoveConstraintFromType() migration.Migrate {
	return migration.Migrate{
		UP: func(d migration.Datasource) error {
			// write your migrations here
			query := "ALTER TABLE shortlink.link ALTER COLUMN \"type\" DROP NOT NULL;"
			query = query + "ALTER TABLE shortlink.link ALTER COLUMN \"path\" DROP NOT NULL;"
			query = query + "ALTER TABLE shortlink.link ALTER COLUMN \"original_url\" DROP NOT NULL;"
			_, err := d.SQL.Exec(query)
			if err != nil {
				return err
			}
			return nil
		},
	}
}
