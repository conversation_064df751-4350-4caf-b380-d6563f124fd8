package migrations

import (
	"fmt"

	"gofr.dev/pkg/gofr/migration"
)

func AddTimestamps() migration.Migrate {

	return migration.Migrate{
		UP: func(d migration.Datasource) error {
			fmt.Println("AddTimestamps migration: Starting migration execution")

			queries := []string{
				`ALTER TABLE shortlink.link ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL;`,
				`ALTER TABLE shortlink.link ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP NULL;`,
				`ALTER TABLE shortlink.link ADD COLUMN IF NOT EXISTS inserted_at TIMESTAMP NULL;`,
			}

			for i, query := range queries {
				fmt.Printf("AddTimestamps migration: Executing query %d: %s\n", i+1, query)
				_, err := d.SQL.Exec(query)
				if err != nil {
					fmt.Printf("AddTimestamps migration: Error executing query %d: %v\n", i+1, err)
					return err
				}
				fmt.Printf("AddTimestamps migration: Successfully executed query %d\n", i+1)
			}

			fmt.Printf("AddTimestamps migration: Migration completed successfully")
			return nil
		},
	}
}
