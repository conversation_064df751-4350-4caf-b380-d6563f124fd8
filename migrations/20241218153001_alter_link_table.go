package migrations

import "gofr.dev/pkg/gofr/migration"

const alterTable = `ALTER TABLE shortlink.link
    ALTER COLUMN type TYPE varchar(32),
    ALTER COLUMN path TYPE varchar(128),
    ALTER COLUMN original_url TYPE varchar(2048);`

func alterTableLink() migration.Migrate {
	return migration.Migrate{
		UP: func(d migration.Datasource) error {
			_, err := d.SQL.Exec(alterTable)
			if err != nil {
				return err
			}
			return nil
		},
	}
}
