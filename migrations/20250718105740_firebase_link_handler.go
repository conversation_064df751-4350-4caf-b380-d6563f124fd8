package migrations

import (
	"fmt"

	"gofr.dev/pkg/gofr/migration"
)

func FirebaseLinkHandler() migration.Migrate {
	return migration.Migrate{
		UP: func(d migration.Datasource) error {
			// Add logging to debug migration execution
			fmt.Println("FirebaseLinkHandler migration: Starting migration execution")

			queries := []string{
				`ALTER TABLE shortlink.link ADD COLUMN IF NOT EXISTS src_url VARCHAR(2048);`,
				`ALTER TABLE shortlink.link ADD COLUMN IF NOT EXISTS target_url VARCHAR(2048);`,
			}

			for i, query := range queries {
				fmt.Printf("FirebaseLinkHandler migration: Executing query %d: %s\n", i+1, query)
				_, err := d.SQL.Exec(query)
				if err != nil {
					fmt.Printf("FirebaseLinkHandler migration: Error executing query %d: %v\n", i+1, err)
					return err
				}
				fmt.Printf("FirebaseLinkHandler migration: Successfully executed query %d\n", i+1)
			}

			fmt.Printf("FirebaseLinkHandler migration: Migration completed successfully")
			return nil
		},
	}
}
