>>>> Makefile
# https://clarkgrubb.com/makefile-style-guide

# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Version:                                                                   0.1 ║
# ║ Date of Version:                                                    31.10.2024 ║
# ║ Owner:                                                                      SD ║
# ║ Classification:                                                       Internal ║
# ║ Distribution:                                                        All Staff ║
# ╚════════════════════════════════════════════════════════════════════════════════╝

MAKEFLAGS += --warn-undefined-variables --no-builtin-rules
SHELL := /usr/bin/env bash
.SHELLFLAGS := -uo pipefail -c
.DEFAULT_GOAL := help

.PHONY: help
help:
	@echo "Usage: make <start|tidy>"
	@echo "start:		Start the server"
	@echo "tidy:		Tidy the modules setup"

.PHONY: start
start:
	APP_ENV="local" go run cmd/web/*.go

.PHONY: tidy
tidy:
	go mod tidy

.PHONY: docker-build
docker-build:
	docker build -t shortlink-service . -f ./infrastructure/Dockerfile

.PHONY: docker-run
docker-run:
	docker run -it --rm -p 8080:8080 --name my-shortlink-service shortlink-service

>>>> README.md
# Short Links Service

A Go-based service for managing and redirecting short URLs to their original destinations.

## API

### Short link creation API

The link creation endpoint is API key protected.

There are two ways for creation short link for now. See the following examples for more details:

```bash
  curl -k -X POST $BASE_URL/links \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: f0e1abbd-XXX-XXX-XXX-22d19a11230f" \
  -d '{
    "target_url": "https://stagedates.com/events/star-fm-rooftop-party-nhow-berlin-20250726-vDO1Q"    
  }'
```


```bash
  curl -k -X POST $BASE_URL/links \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: f0e1abbd-XXX-XXX-XXX-22d19a11230f" \
  -d '{
    "target_url": "https://stagedates.com/events/star-fm-rooftop-party-nhow-berlin-20250726-vDO1Q",
    "shortlink": "https://link.dev.stagedat.es/abcd"
  }'
```

... where BASE_URL equals https://link.dev.stagedat.es for now, on dev environment:

```bash
BASE_URL="https://link.dev.stagedat.es"
```

## Middleware and components in use

### Web Framework & Core
- [gofr](https://gofr.dev/) - Go web framework with built-in observability

### Messaging & Event Processing  
- [Google Cloud Pub/Sub](https://cloud.google.com/pubsub) - Message queue for event processing
- [Redis](https://github.com/redis/go-redis) - In-memory data store for caching

### Security & Session Management
- [SCS Session Manager](https://github.com/alexedwards/scs) - HTTP session management
- [NoSurf](https://github.com/justinas/nosurf) - CSRF protection middleware
- [JWT](https://github.com/golang-jwt/jwt) - JSON Web Token authentication (via gofr)

### Database & Storage
- [PostgreSQL Driver](https://github.com/lib/pq) - PostgreSQL database connectivity (via gofr)

### Observability & Monitoring
- [Prometheus](https://prometheus.io/) - Metrics collection and monitoring (via gofr)
- [OpenTelemetry](https://opentelemetry.io/) - Distributed tracing and observability (via gofr)

### Utilities
- [Google UUID](https://github.com/google/uuid) - UUID generation for unique identifiers

## Troubleshooting

### Database: Workaround for search_path on PostgreSQL

gofr.dev doesn't currently support overriding `postgresql` connection parameters or options like `search_path`. However, this is needed in order to isolate this application at schema level. Therefore the following workaround is recommended: 

```sql
ALTER ROLE shortlinksvc SET search_path TO shortlink;
```

## Running the Service Locally

This project uses the `start.sh` script to simplify common development tasks. Here's how to get the service running:

1. **Explore Script Options:**

```bash
./start.sh --help 
```

2.  **Start Essential Background Services:**

```bash
./start.sh --start-docker-compose-env
```

3.  **Run the Short Link Application:**

```bash
./start.sh --start-local-short-link-service  
```

>>>> cmd/web/logging.go
package main

import (
	"gofr.dev/pkg/gofr"
)

func LogInfo(app *gofr.App, msg string, args ...interface{}) {
	app.Logger().Infof(msg, args...)
}

func LogDebug(app *gofr.App, msg string, args ...interface{}) {
	app.Logger().Debugf(msg, args...)
}

func LogError(app *gofr.App, msg string, args ...interface{}) {
	app.Logger().Errorf(msg, args...)
}

>>>> cmd/web/main.go
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"log/slog"
	"net/http"
	"os"
	"time"

	"github.com/stagedates/shortlink-service/migrations"
	"github.com/stagedates/shortlink-service/pkg/config"
	"github.com/stagedates/shortlink-service/pkg/handlers"
	"github.com/stagedates/shortlink-service/pkg/render"
	"github.com/stagedates/shortlink-service/pkg/serviceclients"

	"github.com/alexedwards/scs/v2"

	// "gofr.dev/examples/using-publisher/migrations"
	// "gofr.dev/pkg/gofr/migration"

	"github.com/redis/go-redis/v9"
	"gofr.dev/pkg/gofr"
	gofrHTTP "gofr.dev/pkg/gofr/http"
)

var ac config.AppConfig
var session *scs.SessionManager
var redisClient *redis.Client

const (
	ExitServiceUnavailable = 1
	ExitConfigError        = 2
	ExitNetworkError       = 3
	// ... other exit codes as needed ...
)

var RequestApiKey string

// Define custom middleware function to access headers and set context
func customMiddleware() gofrHTTP.Middleware {
	return func(inner http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			apiKey := r.Header.Get("X-Api-Key")

			logger := slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
				Level: slog.LevelDebug,
			}))
			logger.Debug("received request with API key", "apiKey", apiKey)

			// Set the API key in the request context for gofr
			ctx := r.Context()
			ctx = context.WithValue(ctx, "RequestApiKey", apiKey)
			r = r.WithContext(ctx)

			inner.ServeHTTP(w, r)
		})
	}
}

// main is the main function
func main() {
	// change this to true when in production
	ac.InProduction = false

	// set up the session
	session = scs.New()
	session.Lifetime = 24 * time.Hour
	session.Cookie.Persist = true
	session.Cookie.SameSite = http.SameSiteLaxMode
	session.Cookie.Secure = ac.InProduction

	ac.Session = session

	tc, err := render.CreateTemplateCache()
	if err != nil {
		log.Fatal("cannot create template cache")
	}

	ac.TemplateCache = tc
	ac.UseCache = false

	repo := handlers.NewRepo(&ac)
	handlers.NewHandlers(repo)

	render.NewTemplates(&ac)

	app := gofr.New()
	app.UseMiddleware(customMiddleware())

	// Run PostgreSQL migrations
	app.Migrate(migrations.All())

	// Setup PubSub processing
	// if err := setupPubSubProcessing(gofrapp, ctx, eventsClient, errChan, cancel); err != nil {
	// 	LogError(gofrapp, "Failed to set up Pub/Sub processing: %v", err)
	// }

	// Start the web server
	LogInfo(app, "Starting web server in GoFr mode...")

	// Register routes before starting the server
	initRoutes(app)

	// Start the main loop, this will run until SIGINT or SIGTERM is received
	// Add a new cancel context to handle teardown of extra applications, eg. Pub/Sub processing
	app.Run()
}

// setupPubSubProcessing initializes and configures PubSub message processing
func setupPubSubProcessing(gofrapp *gofr.App, ctx context.Context, errChan chan error, cancel context.CancelFunc) error {
	// Start Pub/Sub processing in a separate goroutine
	go func() {
		LogInfo(gofrapp, "Starting Pub/Sub message processing...")
		err := handlers.HandleMessagesWithGooglesPubSubClient(gofrapp, ctx, "shortlink-worker.events.events", "stdts-dev", func(msg []byte) {
			// Parse message to check operation type
			var payload struct {
				Data struct {
					Operation string `json:"operation"`
				} `json:"data"`
			}

			if err := json.Unmarshal(msg, &payload); err == nil {
				switch payload.Data.Operation {
				case "update", "createEvent":
					main_event_processing_from_web(gofrapp, msg)
				default:
					LogInfo(gofrapp, "Skipping message with operation: %s", payload.Data.Operation)
				}
			}
		})
		if err != nil {
			errChan <- fmt.Errorf("pub/sub processing error: %v", err)
			cancel()
		}
	}()

	return nil
}

// TODO: move to better location
func main_event_processing_from_web(gofrapp *gofr.App, msg []byte) {
	var payload struct {
		Data struct {
			ID string `json:"id"`
		} `json:"data"`
	}

	if err := json.Unmarshal(msg, &payload); err == nil && payload.Data.ID != "" {
		LogInfo(gofrapp, "ID: %s\n", payload.Data.ID)

		// Fetch event details using GetEvent
		eventsClient, err := serviceclients.NewEventsClient(gofrapp.Config.GetOrDefault("EVENTS_SERVICE_BASE_URL", "http://localhost:8080"))
		event, err := eventsClient.GetEvent(gofrapp, payload.Data.ID)
		if err != nil {
			gofrapp.Logger().Errorf("Error getting event: %v", err)
		} else {
			LogInfo(gofrapp, "\n--- Event Details ---")
			LogInfo(gofrapp, "Title: %s", event.Title)
			LogInfo(gofrapp, "Subtitle: %s", event.Subtitle)
			LogInfo(gofrapp, "Description: %s", event.Description)
			LogInfo(gofrapp, "Thumbnail URL: %s", event.ThumbnailURL)
			LogInfo(gofrapp, "Cover URL: %s", event.CoverURL)
			LogInfo(gofrapp, "Slug: %s", event.Slug)
			LogInfo(gofrapp, "Thumbnail Path: %s", event.ThumbnailPath)
			LogInfo(gofrapp, "Is Draft: %v", event.IsDraft)

			LogInfo(gofrapp, handlers.OpenGraph(event.Title, event.Description, event.ThumbnailURL, event.Slug))
		}
	}
}

// TODO: move to better location
func main_event_processing_from_pubsub(gofrapp *gofr.App) error {
	ctx := context.Background()
	err := handlers.HandleMessagesWithGooglesPubSubClient(gofrapp, ctx, "shortlink-worker.events.events", "stdts-dev", func(msg []byte) {
		main_event_processing_from_web(gofrapp, msg)
	})
	if err != nil {
		gofrapp.Logger().Fatalf("Error handling messages: %v", err)
		return err
	}
	return nil
}

// setupRedisConnection initializes and verifies the Redis connection
func setupRedisConnection(gofrapp *gofr.App) (*redis.Client, error) {
	redisEndpoint := gofrapp.Config.GetOrDefault("REDIS_ENDPOINT_HOST_AND_PORT", "localhost:6379")

	client := redis.NewClient(&redis.Options{
		Addr: redisEndpoint,
	})

	// Verify Redis connection
	if _, err := client.Ping(context.Background()).Result(); err != nil {
		gofrapp.Logger().Errorf("Failed to connect to Redis: %v", err)
		return nil, err
	}

	gofrapp.Logger().Info("Successfully connected to Redis")
	return client, nil
}

>>>> cmd/web/middleware.go
package main

import (
	"fmt"
	"net/http"

	"github.com/justinas/nosurf"
)

// NoSurf is the csrf protection middleware
func NoSurf(next http.Handler) http.Handler {
	csrfHandler := nosurf.New(next)

	csrfHandler.SetBaseCookie(http.Cookie{
		HttpOnly: true,
		Path:     "/",
		Secure:   ac.InProduction,
		SameSite: http.SameSiteLaxMode,
	})
	return csrfHandler
}

func WriteToConsole(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		fmt.Println(r.URL)
		next.ServeHTTP(w, r)
	})
}

// SessionLoad loads and saves session data for current request
func SessionLoad(next http.Handler) http.Handler {
	return session.LoadAndSave(next)
}

func SendCustomHeader(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Set a custom header
		w.Header().Set("X-Custom-Header", "hello header")
		next.ServeHTTP(w, r)
	})
}

>>>> cmd/web/pubsub.go
package main

/* func InitPubSubSubsystem(gofrapp *gofr.App) {
	// Configure PubSub
	pubsubConfig := pubsub.Config{
		Driver: "google-pubsub",
		URL:    os.Getenv("PUBSUB_EMULATOR_HOST"),
	}

	// Add PubSub to the application
	err := gofrapp.AddPubSub(pubsubConfig)
	if err != nil {
		fmt.Printf("Failed to add PubSub: %v\n", err)
		os.Exit(1)
	}

	// Subscribe to the topic
	app.Subscribe("projects/stdts-dev/topics/events.events", handleMessage)

} */

>>>> cmd/web/routes.go
package main

import (
	"github.com/stagedates/shortlink-service/pkg/handlers"
	"gofr.dev/pkg/gofr"
	// "gofr.dev/pkg/gofr"
)

func initRoutes(app *gofr.App) {
	LogInfo(app, "initRoutes(): Registering routes...")
	// Register the preview link handler with the event client
	app.GET("/e/{identifier}", handlers.EventPreviewLinkHandler(app))

	//gofrapp.GET("/healthz", handlers.HealthHandler)
	//gofrapp.GET("/health", handlers.HealthHandler)
	app.GET("/", handlers.FirebaseLinkHandler)
	app.GET("/{identifier}", handlers.FirebaseLinkHandler)
	app.POST("/links", handlers.FirebaseLinkCreationHandler(app))

	LogInfo(app, "initRoutes(): done registering routes.")
}

>>>> configs/README.md
# This Folder

This folder is for gofr.dev required configurations. For more details see the following resources:

* https://gofr.dev/docs/quick-start/configuration
* https://gofr.dev/docs/references/configs

>>>> docker-compose.yaml
version: '3.8'

services:

  db:
    image: docker.io/postgres:latest
    container_name: ${CONTAINER_NAME_PREFIX}-postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      PGUSER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      # - ./roles.sql:/docker-entrypoint-initdb.d/00-roles.sql:ro
      #- ${GRANT}:/docker-entrypoint-initdb.d/01-grant.sql:ro
      #- ${SQL_DUMP}:/docker-entrypoint-initdb.d/02-init.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "sh -c 'pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}'"]
      interval: 5s
      timeout: 5s
      retries: 3
      start_period: 15s
    restart: always
    ports:
      # - "${POSTGRES_PORT}:${POSTGRES_PORT}"
      - "5432:5432"
    networks:
      - shortlinks-service

  pgadmin:
    image: dpage/pgadmin4
    container_name: ${CONTAINER_NAME_PREFIX}-pgadmin4
    ports:
      - 8080:80
    # For local development a separate pgadmin4 password is not needed
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_CONFIG_SERVER_MODE=False
      - PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED=False
    restart: unless-stopped
    volumes:
      - ./servers.json:/pgadmin4/servers.json
    networks:
      - shortlinks-service

  ### REDIS ###
  redis:
    image: docker.io/redis:latest
    container_name: ${CONTAINER_NAME_PREFIX}-redis
    ports:
      - ${REDIS_PORT:-6379}:6379
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    restart: always
    networks:
      - shortlinks-service

volumes:
  pgadmin:
  postgres_data:

networks:
  shortlinks-service:

>>>> docker.sh
#!/bin/bash

IMAGE="v61"

function start_docker_build_image(){
    source ./.env
    printf "docker build \
-t shortlink-test-build \
-t europe-west3-docker.pkg.dev/stdts-dev/shortlink-service/shortlink-service:latest \
-t europe-west3-docker.pkg.dev/stdts-dev/shortlink-service/shortlink-service:$IMAGE \
-f infrastructure/Dockerfile .\n"
}

function start_docker_push_image(){
    source ./.env
    printf "docker push \
europe-west3-docker.pkg.dev/stdts-dev/shortlink-service/shortlink-service:$IMAGE\n"
    printf "docker push \
europe-west3-docker.pkg.dev/stdts-dev/shortlink-service/shortlink-service:latest\n"

    printf "kubectl \${CONTEXT} \${NS} set image deployment/shortlink shortlink=europe-west3-docker.pkg.dev/stdts-dev/shortlink-service/shortlink-service:$IMAGE\n"
    printf "kubectl \${CONTEXT} \${NS} rollout status deployment/shortlink \n"
    printf "kubectl \${CONTEXT} \${NS} rollout history deployment/shortlink \n"
    printf "kubectl \${CONTEXT} \${NS} rollout undo deployment/shortlink \n"
    printf "kubectl \${CONTEXT} \${NS} rollout status deployment/shortlink --revision=150\n"

    
}

start_docker_build_image
start_docker_push_image

>>>> go.mod
module github.com/stagedates/shortlink-service

go 1.24

toolchain go1.24.0

require (
	cloud.google.com/go/pubsub v1.49.0
	github.com/alexedwards/scs/v2 v2.4.0
	github.com/google/uuid v1.6.0
	github.com/justinas/nosurf v1.1.1
	github.com/redis/go-redis/v9 v9.8.0
	gofr.dev v1.39.1
)

require (
	cloud.google.com/go v0.120.0 // indirect
	cloud.google.com/go/auth v0.16.1 // indirect
	cloud.google.com/go/auth/oauth2adapt v0.2.8 // indirect
	cloud.google.com/go/compute/metadata v0.6.0 // indirect
	cloud.google.com/go/iam v1.4.2 // indirect
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/DATA-DOG/go-sqlmock v1.5.2 // indirect
	github.com/XSAM/otelsql v0.38.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cenkalti/backoff/v4 v4.3.0 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgraph-io/dgo/v210 v210.0.0-20230328113526-b66f8ae53a2d // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/eclipse/paho.mqtt.golang v1.5.0 // indirect
	github.com/felixge/httpsnoop v1.0.4 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-sql-driver/mysql v1.9.2 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang-jwt/jwt/v5 v5.2.2 // indirect
	github.com/google/s2a-go v0.1.9 // indirect
	github.com/googleapis/enterprise-certificate-proxy v0.3.6 // indirect
	github.com/googleapis/gax-go/v2 v2.14.1 // indirect
	github.com/gorilla/mux v1.8.1 // indirect
	github.com/gorilla/websocket v1.5.3 // indirect
	github.com/grpc-ecosystem/go-grpc-middleware v1.4.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.26.1 // indirect
	github.com/joho/godotenv v1.5.1 // indirect
	github.com/klauspost/compress v1.18.0 // indirect
	github.com/lib/pq v1.10.9 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/ncruces/go-strftime v0.1.9 // indirect
	github.com/openzipkin/zipkin-go v0.4.3 // indirect
	github.com/pierrec/lz4/v4 v4.1.22 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_golang v1.22.0 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.62.0 // indirect
	github.com/prometheus/procfs v0.15.1 // indirect
	github.com/redis/go-redis/extra/rediscmd/v9 v9.8.0 // indirect
	github.com/redis/go-redis/extra/redisotel/v9 v9.8.0 // indirect
	github.com/remyoudompheng/bigfft v0.0.0-20230129092748-24d4a6f8daec // indirect
	github.com/segmentio/kafka-go v0.4.47 // indirect
	github.com/stretchr/testify v1.10.0 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	go.opencensus.io v0.24.0 // indirect
	go.opentelemetry.io/auto/sdk v1.1.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.60.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/net/http/httptrace/otelhttptrace v0.60.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.60.0 // indirect
	go.opentelemetry.io/otel v1.35.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.35.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.35.0 // indirect
	go.opentelemetry.io/otel/exporters/prometheus v0.57.0 // indirect
	go.opentelemetry.io/otel/exporters/zipkin v1.35.0 // indirect
	go.opentelemetry.io/otel/metric v1.35.0 // indirect
	go.opentelemetry.io/otel/sdk v1.35.0 // indirect
	go.opentelemetry.io/otel/sdk/metric v1.35.0 // indirect
	go.opentelemetry.io/otel/trace v1.35.0 // indirect
	go.opentelemetry.io/proto/otlp v1.5.0 // indirect
	go.uber.org/mock v0.5.2 // indirect
	golang.org/x/crypto v0.37.0 // indirect
	golang.org/x/exp v0.0.0-20250305212735-054e65f0b394 // indirect
	golang.org/x/net v0.39.0 // indirect
	golang.org/x/oauth2 v0.30.0 // indirect
	golang.org/x/sync v0.14.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/term v0.32.0 // indirect
	golang.org/x/text v0.25.0 // indirect
	golang.org/x/time v0.11.0 // indirect
	google.golang.org/api v0.232.0 // indirect
	google.golang.org/genproto v0.0.0-20250303144028-a0af3efb3deb // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20250313205543-e70fdf4c4cb4 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250428153025-10db94c68c34 // indirect
	google.golang.org/grpc v1.72.0 // indirect
	google.golang.org/protobuf v1.36.6 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	modernc.org/libc v1.62.1 // indirect
	modernc.org/mathutil v1.7.1 // indirect
	modernc.org/memory v1.9.1 // indirect
	modernc.org/sqlite v1.37.0 // indirect
)

>>>> go.sum
cloud.google.com/go v0.26.0/go.mod h1:aQUYkXzVsufM+DwF1aE+0xfcU+56JwCaLick0ClmMTw=
cloud.google.com/go v0.120.0 h1:wc6bgG9DHyKqF5/vQvX1CiZrtHnxJjBlKUyF9nP6meA=
cloud.google.com/go v0.120.0/go.mod h1:/beW32s8/pGRuj4IILWQNd4uuebeT4dkOhKmkfit64Q=
cloud.google.com/go/auth v0.16.1 h1:XrXauHMd30LhQYVRHLGvJiYeczweKQXZxsTbV9TiguU=
cloud.google.com/go/auth v0.16.1/go.mod h1:1howDHJ5IETh/LwYs3ZxvlkXF48aSqqJUM+5o02dNOI=
cloud.google.com/go/auth/oauth2adapt v0.2.8 h1:keo8NaayQZ6wimpNSmW5OPc283g65QNIiLpZnkHRbnc=
cloud.google.com/go/auth/oauth2adapt v0.2.8/go.mod h1:XQ9y31RkqZCcwJWNSx2Xvric3RrU88hAYYbjDWYDL+c=
cloud.google.com/go/compute/metadata v0.6.0 h1:A6hENjEsCDtC1k8byVsgwvVcioamEHvZ4j01OwKxG9I=
cloud.google.com/go/compute/metadata v0.6.0/go.mod h1:FjyFAW1MW0C203CEOMDTu3Dk1FlqW3Rga40jzHL4hfg=
cloud.google.com/go/iam v1.4.2 h1:4AckGYAYsowXeHzsn/LCKWIwSWLkdb0eGjH8wWkd27Q=
cloud.google.com/go/iam v1.4.2/go.mod h1:REGlrt8vSlh4dfCJfSEcNjLGq75wW75c5aU3FLOYq34=
cloud.google.com/go/kms v1.21.1 h1:r1Auo+jlfJSf8B7mUnVw5K0fI7jWyoUy65bV53VjKyk=
cloud.google.com/go/kms v1.21.1/go.mod h1:s0wCyByc9LjTdCjG88toVs70U9W+cc6RKFc8zAqX7nE=
cloud.google.com/go/longrunning v0.6.5 h1:sD+t8DO8j4HKW4QfouCklg7ZC1qC4uzVZt8iz3uTW+Q=
cloud.google.com/go/longrunning v0.6.5/go.mod h1:Et04XK+0TTLKa5IPYryKf5DkpwImy6TluQ1QTLwlKmY=
cloud.google.com/go/pubsub v1.49.0 h1:5054IkbslnrMCgA2MAEPcsN3Ky+AyMpEZcii/DoySPo=
cloud.google.com/go/pubsub v1.49.0/go.mod h1:K1FswTWP+C1tI/nfi3HQecoVeFvL4HUOB1tdaNXKhUY=
filippo.io/edwards25519 v1.1.0 h1:FNf4tywRC1HmFuKW5xopWpigGjJKiJSV0Cqo0cJWDaA=
filippo.io/edwards25519 v1.1.0/go.mod h1:BxyFTGdWcka3PhytdK4V28tE5sGfRvvvRV7EaN4VDT4=
github.com/BurntSushi/toml v0.3.1/go.mod h1:xHWCNGjB5oqiDr8zfno3MHue2Ht5sIBksp03qcyfWMU=
github.com/DATA-DOG/go-sqlmock v1.5.2 h1:OcvFkGmslmlZibjAjaHm3L//6LiuBgolP7OputlJIzU=
github.com/DATA-DOG/go-sqlmock v1.5.2/go.mod h1:88MAG/4G7SMwSE3CeA0ZKzrT5CiOU3OJ+JlNzwDqpNU=
github.com/XSAM/otelsql v0.38.0 h1:zWU0/YM9cJhPE71zJcQ2EBHwQDp+G4AX2tPpljslaB8=
github.com/XSAM/otelsql v0.38.0/go.mod h1:5ePOgcLEkWvZtN9H3GV4BUlPeM3p3pzLDCnRG73X8h8=
github.com/alexedwards/scs/v2 v2.4.0 h1:XfnMamKnvp1muJVNr1WzikQTclopsBXWZtzz0NBjOK0=
github.com/alexedwards/scs/v2 v2.4.0/go.mod h1:ToaROZxyKukJKT/xLcVQAChi5k6+Pn1Gvmdl7h3RRj8=
github.com/alicebob/gopher-json v0.0.0-20230218143504-906a9b012302 h1:uvdUDbHQHO85qeSydJtItA4T55Pw6BtAejd0APRJOCE=
github.com/alicebob/gopher-json v0.0.0-20230218143504-906a9b012302/go.mod h1:SGnFV6hVsYE877CKEZ6tDNTjaSXYUk6QqoIK6PrAtcc=
github.com/alicebob/miniredis/v2 v2.34.0 h1:mBFWMaJSNL9RwdGRyEDoAAv8OQc5UlEhLDQggTglU/0=
github.com/alicebob/miniredis/v2 v2.34.0/go.mod h1:kWShP4b58T1CW0Y5dViCd5ztzrDqRWqM3nksiyXk5s8=
github.com/benbjohnson/clock v1.1.0/go.mod h1:J11/hYXuz8f4ySSvYwY0FKfm+ezbsZBKZxNJlLklBHA=
github.com/beorn7/perks v1.0.1 h1:VlbKKnNfV8bJzeqoa4cOKqO6bYr3WgKZxO8Z16+hsOM=
github.com/beorn7/perks v1.0.1/go.mod h1:G2ZrVWU2WbWT9wwq4/hrbKbnv/1ERSJQ0ibhJ6rlkpw=
github.com/bsm/ginkgo/v2 v2.12.0 h1:Ny8MWAHyOepLGlLKYmXG4IEkioBysk6GpaRTLC8zwWs=
github.com/bsm/ginkgo/v2 v2.12.0/go.mod h1:SwYbGRRDovPVboqFv0tPTcG1sN61LM1Z4ARdbAV9g4c=
github.com/bsm/gomega v1.27.10 h1:yeMWxP2pV2fG3FgAODIY8EiRE3dy0aeFYt4l7wh6yKA=
github.com/bsm/gomega v1.27.10/go.mod h1:JyEr/xRbxbtgWNi8tIEVPUYZ5Dzef52k01W3YH0H+O0=
github.com/cenkalti/backoff/v4 v4.3.0 h1:MyRJ/UdXutAwSAT+s3wNd7MfTIcy71VQueUuFK343L8=
github.com/cenkalti/backoff/v4 v4.3.0/go.mod h1:Y3VNntkOUPxTVeUxJ/G5vcM//AlwfmyYozVcomhLiZE=
github.com/census-instrumentation/opencensus-proto v0.2.1/go.mod h1:f6KPmirojxKA12rnyqOA5BBL4O983OfeGPqjHWSTneU=
github.com/cespare/xxhash/v2 v2.3.0 h1:UL815xU9SqsFlibzuggzjXhog7bL6oX9BbNZnL2UFvs=
github.com/cespare/xxhash/v2 v2.3.0/go.mod h1:VGX0DQ3Q6kWi7AoAeZDth3/j3BFtOZR5XLFGgcrjCOs=
github.com/client9/misspell v0.3.4/go.mod h1:qj6jICC3Q7zFZvVWo7KLAzC3yx5G7kyvSDkc90ppPyw=
github.com/cncf/udpa/go v0.0.0-20191209042840-269d4d468f6f/go.mod h1:M8M6+tZqaGXZJjfX53e64911xZQV5JYwmTeXPW+k8Sc=
github.com/davecgh/go-spew v1.1.0/go.mod h1:J7Y8YcW2NihsgmVo/mv3lAwl/skON4iLHjSsI+c5H38=
github.com/davecgh/go-spew v1.1.1 h1:vj9j/u1bqnvCEfJOwUhtlOARqs3+rkHYY13jYWTU97c=
github.com/davecgh/go-spew v1.1.1/go.mod h1:J7Y8YcW2NihsgmVo/mv3lAwl/skON4iLHjSsI+c5H38=
github.com/dgraph-io/dgo/v210 v210.0.0-20230328113526-b66f8ae53a2d h1:abDbP7XBVgwda+h0J5Qra5p2OQpidU2FdkXvzCKL+H8=
github.com/dgraph-io/dgo/v210 v210.0.0-20230328113526-b66f8ae53a2d/go.mod h1:wKFzULXAPj3U2BDAPWXhSbQQNC6FU1+1/5iika6IY7g=
github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f h1:lO4WD4F/rVNCu3HqELle0jiPLLBs70cWOduZpkS1E78=
github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f/go.mod h1:cuUVRXasLTGF7a8hSLbxyZXjz+1KgoB3wDUb6vlszIc=
github.com/dustin/go-humanize v1.0.1 h1:GzkhY7T5VNhEkwH0PVJgjz+fX1rhBrR7pRT3mDkpeCY=
github.com/dustin/go-humanize v1.0.1/go.mod h1:Mu1zIs6XwVuF/gI1OepvI0qD18qycQx+mFykh5fBlto=
github.com/eclipse/paho.mqtt.golang v1.5.0 h1:EH+bUVJNgttidWFkLLVKaQPGmkTUfQQqjOsyvMGvD6o=
github.com/eclipse/paho.mqtt.golang v1.5.0/go.mod h1:du/2qNQVqJf/Sqs4MEL77kR8QTqANF7XU7Fk0aOTAgk=
github.com/envoyproxy/go-control-plane v0.9.0/go.mod h1:YTl/9mNaCwkRvm6d1a2C3ymFceY/DCBVvsKhRF0iEA4=
github.com/envoyproxy/go-control-plane v0.9.1-0.20191026205805-5f8ba28d4473/go.mod h1:YTl/9mNaCwkRvm6d1a2C3ymFceY/DCBVvsKhRF0iEA4=
github.com/envoyproxy/go-control-plane v0.9.4/go.mod h1:6rpuAdCZL397s3pYoYcLgu1mIlRU8Am5FuJP05cCM98=
github.com/envoyproxy/protoc-gen-validate v0.1.0/go.mod h1:iSmxcyjqTsJpI2R4NaDN7+kN2VEUnK/pcBlmesArF7c=
github.com/felixge/httpsnoop v1.0.4 h1:NFTV2Zj1bL4mc9sqWACXbQFVBBg2W3GPvqp8/ESS2Wg=
github.com/felixge/httpsnoop v1.0.4/go.mod h1:m8KPJKqk1gH5J9DgRY2ASl2lWCfGKXixSwevea8zH2U=
github.com/go-kit/log v0.1.0/go.mod h1:zbhenjAZHb184qTLMA9ZjW7ThYL0H2mk7Q6pNt4vbaY=
github.com/go-logfmt/logfmt v0.5.0/go.mod h1:wCYkCAKZfumFQihp8CzCvQ3paCTfi41vtzG1KdI/P7A=
github.com/go-logr/logr v1.2.2/go.mod h1:jdQByPbusPIv2/zmleS9BjJVeZ6kBagPoEUsqbVz/1A=
github.com/go-logr/logr v1.4.2 h1:6pFjapn8bFcIbiKo3XT4j/BhANplGihG6tvd+8rYgrY=
github.com/go-logr/logr v1.4.2/go.mod h1:9T104GzyrTigFIr8wt5mBrctHMim0Nb2HLGrmQ40KvY=
github.com/go-logr/stdr v1.2.2 h1:hSWxHoqTgW2S2qGc0LTAI563KZ5YKYRhT3MFKZMbjag=
github.com/go-logr/stdr v1.2.2/go.mod h1:mMo/vtBO5dYbehREoey6XUKy/eSumjCCveDpRre4VKE=
github.com/go-sql-driver/mysql v1.9.2 h1:4cNKDYQ1I84SXslGddlsrMhc8k4LeDVj6Ad6WRjiHuU=
github.com/go-sql-driver/mysql v1.9.2/go.mod h1:qn46aNg1333BRMNU69Lq93t8du/dwxI64Gl8i5p1WMU=
github.com/go-stack/stack v1.8.0/go.mod h1:v0f6uXyyMGvRgIKkXu+yp6POWl0qKG85gN/melR3HDY=
github.com/gogo/protobuf v1.3.2 h1:Ov1cvc58UF3b5XjBnZv7+opcTcQFZebYjWzi34vdm4Q=
github.com/gogo/protobuf v1.3.2/go.mod h1:P1XiOD3dCwIKUDQYPy72D8LYyHL2YPYrpS2s69NZV8Q=
github.com/golang-jwt/jwt/v5 v5.2.2 h1:Rl4B7itRWVtYIHFrSNd7vhTiz9UpLdi6gZhZ3wEeDy8=
github.com/golang-jwt/jwt/v5 v5.2.2/go.mod h1:pqrtFR0X4osieyHYxtmOUWsAWrfe1Q5UVIyoH402zdk=
github.com/golang/glog v0.0.0-20160126235308-23def4e6c14b/go.mod h1:SBH7ygxi8pfUlaOkMMuAQtPIUF8ecWP5IEl/CR7VP2Q=
github.com/golang/groupcache v0.0.0-20200121045136-8c9f03a8e57e/go.mod h1:cIg4eruTrX1D+g88fzRXU5OdNfaM+9IcxsU14FzY7Hc=
github.com/golang/groupcache v0.0.0-20241129210726-2c02b8208cf8 h1:f+oWsMOmNPc8JmEHVZIycC7hBoQxHH9pNKQORJNozsQ=
github.com/golang/groupcache v0.0.0-20241129210726-2c02b8208cf8/go.mod h1:wcDNUvekVysuuOpQKo3191zZyTpiI6se1N1ULghS0sw=
github.com/golang/mock v1.1.1/go.mod h1:oTYuIxOrZwtPieC+H1uAHpcLFnEyAGVDL/k47Jfbm0A=
github.com/golang/protobuf v1.2.0/go.mod h1:6lQm79b+lXiMfvg/cZm0SGofjICqVBUtrP5yJMmIC1U=
github.com/golang/protobuf v1.3.2/go.mod h1:6lQm79b+lXiMfvg/cZm0SGofjICqVBUtrP5yJMmIC1U=
github.com/golang/protobuf v1.3.3/go.mod h1:vzj43D7+SQXF/4pzW/hwtAqwc6iTitCiVSaWz5lYuqw=
github.com/golang/protobuf v1.4.0-rc.1/go.mod h1:ceaxUfeHdC40wWswd/P6IGgMaK3YpKi5j83Wpe3EHw8=
github.com/golang/protobuf v1.4.0-rc.1.0.20200221234624-67d41d38c208/go.mod h1:xKAWHe0F5eneWXFV3EuXVDTCmh+JuBKY0li0aMyXATA=
github.com/golang/protobuf v1.4.0-rc.2/go.mod h1:LlEzMj4AhA7rCAGe4KMBDvJI+AwstrUpVNzEA03Pprs=
github.com/golang/protobuf v1.4.0-rc.4.0.20200313231945-b860323f09d0/go.mod h1:WU3c8KckQ9AFe+yFwt9sWVRKCVIyN9cPHBJSNnbL67w=
github.com/golang/protobuf v1.4.0/go.mod h1:jodUvKwWbYaEsadDk5Fwe5c77LiNKVO9IDvqG2KuDX0=
github.com/golang/protobuf v1.4.1/go.mod h1:U8fpvMrcmy5pZrNK1lt4xCsGvpyWQ/VVv6QDs8UjoX8=
github.com/golang/protobuf v1.4.3/go.mod h1:oDoupMAO8OvCJWAcko0GGGIgR6R6ocIYbsSw735rRwI=
github.com/golang/protobuf v1.5.4 h1:i7eJL8qZTpSEXOPTxNKhASYpMn+8e5Q6AdndVa1dWek=
github.com/golang/protobuf v1.5.4/go.mod h1:lnTiLA8Wa4RWRcIUkrtSVa5nRhsEGBg48fD6rSs7xps=
github.com/google/go-cmp v0.2.0/go.mod h1:oXzfMopK8JAjlY9xF4vHSVASa0yLyX7SntLO5aqRK0M=
github.com/google/go-cmp v0.3.0/go.mod h1:8QqcDgzrUqlUb/G2PQTWiueGozuR1884gddMywk6iLU=
github.com/google/go-cmp v0.3.1/go.mod h1:8QqcDgzrUqlUb/G2PQTWiueGozuR1884gddMywk6iLU=
github.com/google/go-cmp v0.4.0/go.mod h1:v8dTdLbMG2kIc/vJvl+f65V22dbkXbowE6jgT/gNBxE=
github.com/google/go-cmp v0.5.0/go.mod h1:v8dTdLbMG2kIc/vJvl+f65V22dbkXbowE6jgT/gNBxE=
github.com/google/go-cmp v0.5.3/go.mod h1:v8dTdLbMG2kIc/vJvl+f65V22dbkXbowE6jgT/gNBxE=
github.com/google/go-cmp v0.7.0 h1:wk8382ETsv4JYUZwIsn6YpYiWiBsYLSJiTsyBybVuN8=
github.com/google/go-cmp v0.7.0/go.mod h1:pXiqmnSA92OHEEa9HXL2W4E7lf9JzCmGVUdgjX3N/iU=
github.com/google/pprof v0.0.0-20250317173921-a4b03ec1a45e h1:ijClszYn+mADRFY17kjQEVQ1XRhq2/JR1M3sGqeJoxs=
github.com/google/pprof v0.0.0-20250317173921-a4b03ec1a45e/go.mod h1:boTsfXsheKC2y+lKOCMpSfarhxDeIzfZG1jqGcPl3cA=
github.com/google/s2a-go v0.1.9 h1:LGD7gtMgezd8a/Xak7mEWL0PjoTQFvpRudN895yqKW0=
github.com/google/s2a-go v0.1.9/go.mod h1:YA0Ei2ZQL3acow2O62kdp9UlnvMmU7kA6Eutn0dXayM=
github.com/google/uuid v1.1.2/go.mod h1:TIyPZe4MgqvfeYDBFedMoGGpEw/LqOeaOT+nhxU+yHo=
github.com/google/uuid v1.6.0 h1:NIvaJDMOsjHA8n1jAhLSgzrAzy1Hgr+hNrb57e+94F0=
github.com/google/uuid v1.6.0/go.mod h1:TIyPZe4MgqvfeYDBFedMoGGpEw/LqOeaOT+nhxU+yHo=
github.com/googleapis/enterprise-certificate-proxy v0.3.6 h1:GW/XbdyBFQ8Qe+YAmFU9uHLo7OnF5tL52HFAgMmyrf4=
github.com/googleapis/enterprise-certificate-proxy v0.3.6/go.mod h1:MkHOF77EYAE7qfSuSS9PU6g4Nt4e11cnsDUowfwewLA=
github.com/googleapis/gax-go/v2 v2.14.1 h1:hb0FFeiPaQskmvakKu5EbCbpntQn48jyHuvrkurSS/Q=
github.com/googleapis/gax-go/v2 v2.14.1/go.mod h1:Hb/NubMaVM88SrNkvl8X/o8XWwDJEPqouaLeN2IUxoA=
github.com/gorilla/mux v1.8.1 h1:TuBL49tXwgrFYWhqrNgrUNEY92u81SPhu7sTdzQEiWY=
github.com/gorilla/mux v1.8.1/go.mod h1:AKf9I4AEqPTmMytcMc0KkNouC66V3BtZ4qD5fmWSiMQ=
github.com/gorilla/websocket v1.5.3 h1:saDtZ6Pbx/0u+bgYQ3q96pZgCzfhKXGPqt7kZ72aNNg=
github.com/gorilla/websocket v1.5.3/go.mod h1:YR8l580nyteQvAITg2hZ9XVh4b55+EU/adAjf1fMHhE=
github.com/grpc-ecosystem/go-grpc-middleware v1.4.0 h1:UH//fgunKIs4JdUbpDl1VZCDaL56wXCB/5+wF6uHfaI=
github.com/grpc-ecosystem/go-grpc-middleware v1.4.0/go.mod h1:g5qyo/la0ALbONm6Vbp88Yd8NsDy6rZz+RcrMPxvld8=
github.com/grpc-ecosystem/grpc-gateway/v2 v2.26.1 h1:e9Rjr40Z98/clHv5Yg79Is0NtosR5LXRvdr7o/6NwbA=
github.com/grpc-ecosystem/grpc-gateway/v2 v2.26.1/go.mod h1:tIxuGz/9mpox++sgp9fJjHO0+q1X9/UOWd798aAm22M=
github.com/joho/godotenv v1.5.1 h1:7eLL/+HRGLY0ldzfGMeQkb7vMd0as4CfYvUVzLqw0N0=
github.com/joho/godotenv v1.5.1/go.mod h1:f4LDr5Voq0i2e/R5DDNOoa2zzDfwtkZa6DnEwAbqwq4=
github.com/justinas/nosurf v1.1.1 h1:92Aw44hjSK4MxJeMSyDa7jwuI9GR2J/JCQiaKvXXSlk=
github.com/justinas/nosurf v1.1.1/go.mod h1:ALpWdSbuNGy2lZWtyXdjkYv4edL23oSEgfBT1gPJ5BQ=
github.com/kisielk/errcheck v1.5.0/go.mod h1:pFxgyoBC7bSaBwPgfKdkLd5X25qrDl4LWUI2bnpBCr8=
github.com/kisielk/gotool v1.0.0/go.mod h1:XhKaO+MFFWcvkIS/tQcRk01m1F5IRFswLeQ+oQHNcck=
github.com/kisielk/sqlstruct v0.0.0-20201105191214-5f3e10d3ab46/go.mod h1:yyMNCyc/Ib3bDTKd379tNMpB/7/H5TjM2Y9QJ5THLbE=
github.com/klauspost/compress v1.15.9/go.mod h1:PhcZ0MbTNciWF3rruxRgKxI5NkcHHrHUDtV4Yw2GlzU=
github.com/klauspost/compress v1.18.0 h1:c/Cqfb0r+Yi+JtIEq73FWXVkRonBlf0CRNYc8Zttxdo=
github.com/klauspost/compress v1.18.0/go.mod h1:2Pp+KzxcywXVXMr50+X0Q/Lsb43OQHYWRCY2AiWywWQ=
github.com/konsorten/go-windows-terminal-sequences v1.0.1/go.mod h1:T0+1ngSBFLxvqU3pZ+m/2kptfBszLMUkC4ZK/EgS/cQ=
github.com/kr/pretty v0.1.0/go.mod h1:dAy3ld7l9f0ibDNOQOHHMYYIIbhfbHSm3C4ZsoJORNo=
github.com/kr/pretty v0.3.1 h1:flRD4NNwYAUpkphVc1HcthR4KEIFJ65n8Mw5qdRn3LE=
github.com/kr/pretty v0.3.1/go.mod h1:hoEshYVHaxMs3cyo3Yncou5ZscifuDolrwPKZanG3xk=
github.com/kr/pty v1.1.1/go.mod h1:pFQYn66WHrOpPYNljwOMqo10TkYh1fy3cYio2l3bCsQ=
github.com/kr/text v0.1.0/go.mod h1:4Jbv+DJW3UT/LiOwJeYQe1efqtUx/iVham/4vfdArNI=
github.com/kr/text v0.2.0 h1:5Nx0Ya0ZqY2ygV366QzturHI13Jq95ApcVaJBhpS+AY=
github.com/kr/text v0.2.0/go.mod h1:eLer722TekiGuMkidMxC/pM04lWEeraHUUmBw8l2grE=
github.com/kylelemons/godebug v1.1.0 h1:RPNrshWIDI6G2gRW9EHilWtl7Z6Sb1BR0xunSBf0SNc=
github.com/kylelemons/godebug v1.1.0/go.mod h1:9/0rRGxNHcop5bhtWyNeEfOS8JIWk580+fNqagV/RAw=
github.com/lib/pq v1.10.9 h1:YXG7RB+JIjhP29X+OtkiDnYaXQwpS4JEWq7dtCCRUEw=
github.com/lib/pq v1.10.9/go.mod h1:AlVN5x4E4T544tWzH6hKfbfQvm3HdbOxrmggDNAPY9o=
github.com/mattn/go-isatty v0.0.20 h1:xfD0iDuEKnDkl03q4limB+vH+GxLEtL/jb4xVJSWWEY=
github.com/mattn/go-isatty v0.0.20/go.mod h1:W+V8PltTTMOvKvAeJH7IuucS94S2C6jfK/D7dTCTo3Y=
github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 h1:C3w9PqII01/Oq1c1nUAm88MOHcQC9l5mIlSMApZMrHA=
github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822/go.mod h1:+n7T8mK8HuQTcFwEeznm/DIxMOiR9yIdICNftLE1DvQ=
github.com/ncruces/go-strftime v0.1.9 h1:bY0MQC28UADQmHmaF5dgpLmImcShSi2kHU9XLdhx/f4=
github.com/ncruces/go-strftime v0.1.9/go.mod h1:Fwc5htZGVVkseilnfgOVb9mKy6w1naJmn9CehxcKcls=
github.com/opentracing/opentracing-go v1.1.0/go.mod h1:UkNAQd3GIcIGf0SeVgPpRdFStlNbqXla1AfSYxPUl2o=
github.com/openzipkin/zipkin-go v0.4.3 h1:9EGwpqkgnwdEIJ+Od7QVSEIH+ocmm5nPat0G7sjsSdg=
github.com/openzipkin/zipkin-go v0.4.3/go.mod h1:M9wCJZFWCo2RiY+o1eBCEMe0Dp2S5LDHcMZmk3RmK7c=
github.com/pierrec/lz4/v4 v4.1.15/go.mod h1:gZWDp/Ze/IJXGXf23ltt2EXimqmTUXEy0GFuRQyBid4=
github.com/pierrec/lz4/v4 v4.1.22 h1:cKFw6uJDK+/gfw5BcDL0JL5aBsAFdsIT18eRtLj7VIU=
github.com/pierrec/lz4/v4 v4.1.22/go.mod h1:gZWDp/Ze/IJXGXf23ltt2EXimqmTUXEy0GFuRQyBid4=
github.com/pkg/errors v0.8.1/go.mod h1:bwawxfHBFNV+L2hUp1rHADufV3IMtnDRdf1r5NINEl0=
github.com/pkg/errors v0.9.1 h1:FEBLx1zS214owpjy7qsBeixbURkuhQAwrK5UwLGTwt4=
github.com/pkg/errors v0.9.1/go.mod h1:bwawxfHBFNV+L2hUp1rHADufV3IMtnDRdf1r5NINEl0=
github.com/pmezard/go-difflib v1.0.0 h1:4DBwDE0NGyQoBHbLQYPwSUPoCMWR5BEzIk/f1lZbAQM=
github.com/pmezard/go-difflib v1.0.0/go.mod h1:iKH77koFhYxTK1pcRnkKkqfTogsbg7gZNVY4sRDYZ/4=
github.com/prometheus/client_golang v1.22.0 h1:rb93p9lokFEsctTys46VnV1kLCDpVZ0a/Y92Vm0Zc6Q=
github.com/prometheus/client_golang v1.22.0/go.mod h1:R7ljNsLXhuQXYZYtw6GAE9AZg8Y7vEW5scdCXrWRXC0=
github.com/prometheus/client_model v0.0.0-20190812154241-14fe0d1b01d4/go.mod h1:xMI15A0UPsDsEKsMN9yxemIoYk6Tm2C1GtYGdfGttqA=
github.com/prometheus/client_model v0.6.1 h1:ZKSh/rekM+n3CeS952MLRAdFwIKqeY8b62p8ais2e9E=
github.com/prometheus/client_model v0.6.1/go.mod h1:OrxVMOVHjw3lKMa8+x6HeMGkHMQyHDk9E3jmP2AmGiY=
github.com/prometheus/common v0.62.0 h1:xasJaQlnWAeyHdUBeGjXmutelfJHWMRr+Fg4QszZ2Io=
github.com/prometheus/common v0.62.0/go.mod h1:vyBcEuLSvWos9B1+CyL7JZ2up+uFzXhkqml0W5zIY1I=
github.com/prometheus/procfs v0.15.1 h1:YagwOFzUgYfKKHX6Dr+sHT7km/hxC76UB0learggepc=
github.com/prometheus/procfs v0.15.1/go.mod h1:fB45yRUv8NstnjriLhBQLuOUt+WW4BsoGhij/e3PBqk=
github.com/redis/go-redis/extra/rediscmd/v9 v9.8.0 h1:/A+PnpT6ufTUt/6YPXiZlCRoyyfEnDag5WGrEK8Gq0I=
github.com/redis/go-redis/extra/rediscmd/v9 v9.8.0/go.mod h1:FGO4BNjl5TfH9U771826GIW2Ul4pOEqHAN+0xjfw+dU=
github.com/redis/go-redis/extra/redisotel/v9 v9.8.0 h1:mnKrl8WqyGJK4pletf2itS+Te/ng3Qm4YjtveY406J8=
github.com/redis/go-redis/extra/redisotel/v9 v9.8.0/go.mod h1:iObamxrrXt4hGWiCWv5BAs68xPYc/MfrLd34H9TaKyk=
github.com/redis/go-redis/v9 v9.8.0 h1:q3nRvjrlge/6UD7eTu/DSg2uYiU2mCL0G/uzBWqhicI=
github.com/redis/go-redis/v9 v9.8.0/go.mod h1:huWgSWd8mW6+m0VPhJjSSQ+d6Nh1VICQ6Q5lHuCH/Iw=
github.com/remyoudompheng/bigfft v0.0.0-20230129092748-24d4a6f8daec h1:W09IVJc94icq4NjY3clb7Lk8O1qJ8BdBEF8z0ibU0rE=
github.com/remyoudompheng/bigfft v0.0.0-20230129092748-24d4a6f8daec/go.mod h1:qqbHyh8v60DhA7CoWK5oRCqLrMHRGoxYCSS9EjAz6Eo=
github.com/rogpeppe/go-internal v1.13.1 h1:KvO1DLK/DRN07sQ1LQKScxyZJuNnedQ5/wKSR38lUII=
github.com/rogpeppe/go-internal v1.13.1/go.mod h1:uMEvuHeurkdAXX61udpOXGD/AzZDWNMNyH2VO9fmH0o=
github.com/segmentio/kafka-go v0.4.47 h1:IqziR4pA3vrZq7YdRxaT3w1/5fvIH5qpCwstUanQQB0=
github.com/segmentio/kafka-go v0.4.47/go.mod h1:HjF6XbOKh0Pjlkr5GVZxt6CsjjwnmhVOfURM5KMd8qg=
github.com/sirupsen/logrus v1.4.2/go.mod h1:tLMulIdttU9McNUspp0xgXVQah82FyeX6MwdIuYE2rE=
github.com/stretchr/objx v0.1.0/go.mod h1:HFkY916IF+rwdDfMAkV7OtwuqBVzrE8GR6GFx+wExME=
github.com/stretchr/objx v0.1.1/go.mod h1:HFkY916IF+rwdDfMAkV7OtwuqBVzrE8GR6GFx+wExME=
github.com/stretchr/objx v0.4.0/go.mod h1:YvHI0jy2hoMjB+UWwv71VJQ9isScKT/TqJzVSSt89Yw=
github.com/stretchr/objx v0.5.0/go.mod h1:Yh+to48EsGEfYuaHDzXPcE3xhTkx73EhmCGUpEOglKo=
github.com/stretchr/objx v0.5.2 h1:xuMeJ0Sdp5ZMRXx/aWO6RZxdr3beISkG5/G/aIRr3pY=
github.com/stretchr/objx v0.5.2/go.mod h1:FRsXN1f5AsAjCGJKqEizvkpNtU+EGNCLh3NxZ/8L+MA=
github.com/stretchr/testify v1.2.2/go.mod h1:a8OnRcib4nhh0OaRAV+Yts87kKdq0PP7pXfy6kDkUVs=
github.com/stretchr/testify v1.3.0/go.mod h1:M5WIy9Dh21IEIfnGCwXGc5bZfKNJtfHm1UVUgZn+9EI=
github.com/stretchr/testify v1.4.0/go.mod h1:j7eGeouHqKxXV5pUuKE4zz7dFj8WfuZ+81PSLYec5m4=
github.com/stretchr/testify v1.7.0/go.mod h1:6Fq8oRcR53rry900zMqJjRRixrwX3KX962/h/Wwjteg=
github.com/stretchr/testify v1.7.1/go.mod h1:6Fq8oRcR53rry900zMqJjRRixrwX3KX962/h/Wwjteg=
github.com/stretchr/testify v1.8.0/go.mod h1:yNjHg4UonilssWZ8iaSj1OCr/vHnekPRkoO+kdMU+MU=
github.com/stretchr/testify v1.8.1/go.mod h1:w2LPCIKwWwSfY2zedu0+kehJoqGctiVI29o6fzry7u4=
github.com/stretchr/testify v1.10.0 h1:Xv5erBjTwe/5IxqUQTdXv5kgmIvbHo3QQyRwhJsOfJA=
github.com/stretchr/testify v1.10.0/go.mod h1:r2ic/lqez/lEtzL7wO/rwa5dbSLXVDPFyf8C91i36aY=
github.com/xdg-go/pbkdf2 v1.0.0 h1:Su7DPu48wXMwC3bs7MCNG+z4FhcyEuz5dlvchbq0B0c=
github.com/xdg-go/pbkdf2 v1.0.0/go.mod h1:jrpuAogTd400dnrH08LKmI/xc1MbPOebTwRqcT5RDeI=
github.com/xdg-go/scram v1.1.2 h1:FHX5I5B4i4hKRVRBCFRxq1iQRej7WO3hhBuJf+UUySY=
github.com/xdg-go/scram v1.1.2/go.mod h1:RT/sEzTbU5y00aCK8UOx6R7YryM0iF1N2MOmC3kKLN4=
github.com/xdg-go/stringprep v1.0.4 h1:XLI/Ng3O1Atzq0oBs3TWm+5ZVgkq2aqdlvP9JtoZ6c8=
github.com/xdg-go/stringprep v1.0.4/go.mod h1:mPGuuIYwz7CmR2bT9j4GbQqutWS1zV24gijq1dTyGkM=
github.com/yuin/goldmark v1.1.27/go.mod h1:3hX8gzYuyVAZsxl0MRgGTJEmQBFcNTphYh9decYSb74=
github.com/yuin/goldmark v1.2.1/go.mod h1:3hX8gzYuyVAZsxl0MRgGTJEmQBFcNTphYh9decYSb74=
github.com/yuin/goldmark v1.4.13/go.mod h1:6yULJ656Px+3vBD8DxQVa3kxgyrAnzto9xy5taEt/CY=
github.com/yuin/gopher-lua v1.1.1 h1:kYKnWBjvbNP4XLT3+bPEwAXJx262OhaHDWDVOPjL46M=
github.com/yuin/gopher-lua v1.1.1/go.mod h1:GBR0iDaNXjAgGg9zfCvksxSRnQx76gclCIb7kdAd1Pw=
go.einride.tech/aip v0.68.1 h1:16/AfSxcQISGN5z9C5lM+0mLYXihrHbQ1onvYTr93aQ=
go.einride.tech/aip v0.68.1/go.mod h1:XaFtaj4HuA3Zwk9xoBtTWgNubZ0ZZXv9BZJCkuKuWbg=
go.opencensus.io v0.24.0 h1:y73uSU6J157QMP2kn2r30vwW1A2W2WFwSCGnAVxeaD0=
go.opencensus.io v0.24.0/go.mod h1:vNK8G9p7aAivkbmorf4v+7Hgx+Zs0yY+0fOtgBfjQKo=
go.opentelemetry.io/auto/sdk v1.1.0 h1:cH53jehLUN6UFLY71z+NDOiNJqDdPRaXzTel0sJySYA=
go.opentelemetry.io/auto/sdk v1.1.0/go.mod h1:3wSPjt5PWp2RhlCcmmOial7AvC4DQqZb7a7wCow3W8A=
go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.60.0 h1:x7wzEgXfnzJcHDwStJT+mxOz4etr2EcexjqhBvmoakw=
go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.60.0/go.mod h1:rg+RlpR5dKwaS95IyyZqj5Wd4E13lk/msnTS0Xl9lJM=
go.opentelemetry.io/contrib/instrumentation/net/http/httptrace/otelhttptrace v0.60.0 h1:0tY123n7CdWMem7MOVdKOt0YfshufLCwfE5Bob+hQuM=
go.opentelemetry.io/contrib/instrumentation/net/http/httptrace/otelhttptrace v0.60.0/go.mod h1:CosX/aS4eHnG9D7nESYpV753l4j9q5j3SL/PUYd2lR8=
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.60.0 h1:sbiXRNDSWJOTobXh5HyQKjq6wUC5tNybqjIqDpAY4CU=
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.60.0/go.mod h1:69uWxva0WgAA/4bu2Yy70SLDBwZXuQ6PbBpbsa5iZrQ=
go.opentelemetry.io/otel v1.35.0 h1:xKWKPxrxB6OtMCbmMY021CqC45J+3Onta9MqjhnusiQ=
go.opentelemetry.io/otel v1.35.0/go.mod h1:UEqy8Zp11hpkUrL73gSlELM0DupHoiq72dR+Zqel/+Y=
go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.35.0 h1:1fTNlAIJZGWLP5FVu0fikVry1IsiUnXjf7QFvoNN3Xw=
go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.35.0/go.mod h1:zjPK58DtkqQFn+YUMbx0M2XV3QgKU0gS9LeGohREyK4=
go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.35.0 h1:m639+BofXTvcY1q8CGs4ItwQarYtJPOWmVobfM1HpVI=
go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.35.0/go.mod h1:LjReUci/F4BUyv+y4dwnq3h/26iNOeC3wAIqgvTIZVo=
go.opentelemetry.io/otel/exporters/prometheus v0.57.0 h1:AHh/lAP1BHrY5gBwk8ncc25FXWm/gmmY3BX258z5nuk=
go.opentelemetry.io/otel/exporters/prometheus v0.57.0/go.mod h1:QpFWz1QxqevfjwzYdbMb4Y1NnlJvqSGwyuU0B4iuc9c=
go.opentelemetry.io/otel/exporters/zipkin v1.35.0 h1:OAx1AdClqTB3pz+B4osLuGjx8kubys8ByW7yx0lF454=
go.opentelemetry.io/otel/exporters/zipkin v1.35.0/go.mod h1:hz5wHI9hmCXzwkXFGZ05ObZw2Q2t/AeAZ18PExd2uSM=
go.opentelemetry.io/otel/metric v1.35.0 h1:0znxYu2SNyuMSQT4Y9WDWej0VpcsxkuklLa4/siN90M=
go.opentelemetry.io/otel/metric v1.35.0/go.mod h1:nKVFgxBZ2fReX6IlyW28MgZojkoAkJGaE8CpgeAU3oE=
go.opentelemetry.io/otel/sdk v1.35.0 h1:iPctf8iprVySXSKJffSS79eOjl9pvxV9ZqOWT0QejKY=
go.opentelemetry.io/otel/sdk v1.35.0/go.mod h1:+ga1bZliga3DxJ3CQGg3updiaAJoNECOgJREo9KHGQg=
go.opentelemetry.io/otel/sdk/metric v1.35.0 h1:1RriWBmCKgkeHEhM7a2uMjMUfP7MsOF5JpUCaEqEI9o=
go.opentelemetry.io/otel/sdk/metric v1.35.0/go.mod h1:is6XYCUMpcKi+ZsOvfluY5YstFnhW0BidkR+gL+qN+w=
go.opentelemetry.io/otel/trace v1.35.0 h1:dPpEfJu1sDIqruz7BHFG3c7528f6ddfSWfFDVt/xgMs=
go.opentelemetry.io/otel/trace v1.35.0/go.mod h1:WUk7DtFp1Aw2MkvqGdwiXYDZZNvA/1J8o6xRXLrIkyc=
go.opentelemetry.io/proto/otlp v1.5.0 h1:xJvq7gMzB31/d406fB8U5CBdyQGw4P399D1aQWU/3i4=
go.opentelemetry.io/proto/otlp v1.5.0/go.mod h1:keN8WnHxOy8PG0rQZjJJ5A2ebUoafqWp0eVQ4yIXvJ4=
go.uber.org/atomic v1.7.0/go.mod h1:fEN4uk6kAWBTFdckzkM89CLk9XfWZrxpCo0nPH17wJc=
go.uber.org/goleak v1.1.10/go.mod h1:8a7PlsEVH3e/a/GLqe5IIrQx6GzcnRmZEufDUTk4A7A=
go.uber.org/goleak v1.3.0 h1:2K3zAYmnTNqV73imy9J1T3WC+gmCePx2hEGkimedGto=
go.uber.org/goleak v1.3.0/go.mod h1:CoHD4mav9JJNrW/WLlf7HGZPjdw8EucARQHekz1X6bE=
go.uber.org/mock v0.5.2 h1:LbtPTcP8A5k9WPXj54PPPbjcI4Y6lhyOZXn+VS7wNko=
go.uber.org/mock v0.5.2/go.mod h1:wLlUxC2vVTPTaE3UD51E0BGOAElKrILxhVSDYQLld5o=
go.uber.org/multierr v1.6.0/go.mod h1:cdWPpRnG4AhwMwsgIHip0KRBQjJy5kYEpYjJxpXp9iU=
go.uber.org/zap v1.18.1/go.mod h1:xg/QME4nWcxGxrpdeYfq7UvYrLh66cuVKdrbD1XF/NI=
gofr.dev v1.39.1 h1:ZaT9YbOd/MIViDF69HsZJF/EE8Fh0GxnsvcWhWhTB/E=
gofr.dev v1.39.1/go.mod h1:Yf2/AMOh1pqn9NzzyQ8/PMy3lLA1+dRA01q7hCtC/YY=
golang.org/x/crypto v0.0.0-20190308221718-c2843e01d9a2/go.mod h1:djNgcEr1/C05ACkg1iLfiJU5Ep61QUkGW8qpdssI0+w=
golang.org/x/crypto v0.0.0-20191011191535-87dc89f01550/go.mod h1:yigFU9vqHzYiE8UmvKecakEJjdnWj3jj499lnFckfCI=
golang.org/x/crypto v0.0.0-20200622213623-75b288015ac9/go.mod h1:LzIPMQfyMNhhGPhUkYOs5KpL4U8rLKemX1yGLhDgUto=
golang.org/x/crypto v0.0.0-20210921155107-089bfa567519/go.mod h1:GvvjBRRGRdwPK5ydBHafDWAxML/pGHZbMvKqRZ5+Abc=
golang.org/x/crypto v0.14.0/go.mod h1:MVFd36DqK4CsrnJYDkBA3VC4m2GkXAM0PvzMCn4JQf4=
golang.org/x/crypto v0.37.0 h1:kJNSjF/Xp7kU0iB2Z+9viTPMW4EqqsrywMXLJOOsXSE=
golang.org/x/crypto v0.37.0/go.mod h1:vg+k43peMZ0pUMhYmVAWysMK35e6ioLh3wB8ZCAfbVc=
golang.org/x/exp v0.0.0-20190121172915-509febef88a4/go.mod h1:CJ0aWSM057203Lf6IL+f9T1iT9GByDxfZKAQTCR3kQA=
golang.org/x/exp v0.0.0-20250305212735-054e65f0b394 h1:nDVHiLt8aIbd/VzvPWN6kSOPE7+F/fNFDSXLVYkE/Iw=
golang.org/x/exp v0.0.0-20250305212735-054e65f0b394/go.mod h1:sIifuuw/Yco/y6yb6+bDNfyeQ/MdPUy/hKEMYQV17cM=
golang.org/x/lint v0.0.0-20181026193005-c67002cb31c3/go.mod h1:UVdnD1Gm6xHRNCYTkRU2/jEulfH38KcIWyp/GAMgvoE=
golang.org/x/lint v0.0.0-20190227174305-5b3e6a55c961/go.mod h1:wehouNa3lNwaWXcvxsM5YxQ5yQlVC4a0KAMCusXpPoU=
golang.org/x/lint v0.0.0-20190313153728-d0100b6bd8b3/go.mod h1:6SW0HCj/g11FgYtHlgUYUwCkIfeOF89ocIRzGO/8vkc=
golang.org/x/lint v0.0.0-20190930215403-16217165b5de/go.mod h1:6SW0HCj/g11FgYtHlgUYUwCkIfeOF89ocIRzGO/8vkc=
golang.org/x/mod v0.2.0/go.mod h1:s0Qsj1ACt9ePp/hMypM3fl4fZqREWJwdYDEqhRiZZUA=
golang.org/x/mod v0.3.0/go.mod h1:s0Qsj1ACt9ePp/hMypM3fl4fZqREWJwdYDEqhRiZZUA=
golang.org/x/mod v0.6.0-dev.0.20220419223038-86c51ed26bb4/go.mod h1:jJ57K6gSWd91VN4djpZkiMVwK6gcyfeH4XE8wZrZaV4=
golang.org/x/mod v0.8.0/go.mod h1:iBbtSCu2XBx23ZKBPSOrRkjjQPZFPuis4dIYUhu/chs=
golang.org/x/mod v0.24.0 h1:ZfthKaKaT4NrhGVZHO1/WDTwGES4De8KtWO0SIbNJMU=
golang.org/x/mod v0.24.0/go.mod h1:IXM97Txy2VM4PJ3gI61r1YEk/gAj6zAHN3AdZt6S9Ww=
golang.org/x/net v0.0.0-20180724234803-3673e40ba225/go.mod h1:mL1N/T3taQHkDXs73rZJwtUhF3w3ftmwwsq0BUmARs4=
golang.org/x/net v0.0.0-20180826012351-8a410e7b638d/go.mod h1:mL1N/T3taQHkDXs73rZJwtUhF3w3ftmwwsq0BUmARs4=
golang.org/x/net v0.0.0-20190213061140-3a22650c66bd/go.mod h1:mL1N/T3taQHkDXs73rZJwtUhF3w3ftmwwsq0BUmARs4=
golang.org/x/net v0.0.0-20190311183353-d8887717615a/go.mod h1:t9HGtf8HONx5eT2rtn7q6eTqICYqUVnKs3thJo3Qplg=
golang.org/x/net v0.0.0-20190404232315-eb5bcb51f2a3/go.mod h1:t9HGtf8HONx5eT2rtn7q6eTqICYqUVnKs3thJo3Qplg=
golang.org/x/net v0.0.0-20190620200207-3b0461eec859/go.mod h1:z5CRVTTTmAJ677TzLLGU+0bjPO0LkuOLi4/5GtJWs/s=
golang.org/x/net v0.0.0-20200226121028-0de0cce0169b/go.mod h1:z5CRVTTTmAJ677TzLLGU+0bjPO0LkuOLi4/5GtJWs/s=
golang.org/x/net v0.0.0-20201021035429-f5854403a974/go.mod h1:sp8m0HH+o8qH0wwXwYZr8TS3Oi6o0r6Gce1SSxlDquU=
golang.org/x/net v0.0.0-20201110031124-69a78807bb2b/go.mod h1:sp8m0HH+o8qH0wwXwYZr8TS3Oi6o0r6Gce1SSxlDquU=
golang.org/x/net v0.0.0-20210226172049-e18ecbb05110/go.mod h1:m0MpNAwzfU5UDzcl9v0D8zg8gWTRqZa9RBIspLL5mdg=
golang.org/x/net v0.0.0-20220722155237-a158d28d115b/go.mod h1:XRhObCWvk6IyKnWLug+ECip1KBveYUHfp+8e9klMJ9c=
golang.org/x/net v0.6.0/go.mod h1:2Tu9+aMcznHK/AK1HMvgo6xiTLG5rD5rZLDS+rp2Bjs=
golang.org/x/net v0.10.0/go.mod h1:0qNGK6F8kojg2nk9dLZ2mShWaEBan6FAoqfSigmmuDg=
golang.org/x/net v0.17.0/go.mod h1:NxSsAGuq816PNPmqtQdLE42eU2Fs7NoRIZrHJAlaCOE=
golang.org/x/net v0.39.0 h1:ZCu7HMWDxpXpaiKdhzIfaltL9Lp31x/3fCP11bc6/fY=
golang.org/x/net v0.39.0/go.mod h1:X7NRbYVEA+ewNkCNyJ513WmMdQ3BineSwVtN2zD/d+E=
golang.org/x/oauth2 v0.0.0-20180821212333-d2e6202438be/go.mod h1:N/0e6XlmueqKjAGxoOufVs8QHGRruUQn6yWY3a++T0U=
golang.org/x/oauth2 v0.30.0 h1:dnDm7JmhM45NNpd8FDDeLhK6FwqbOf4MLCM9zb1BOHI=
golang.org/x/oauth2 v0.30.0/go.mod h1:B++QgG3ZKulg6sRPGD/mqlHQs5rB3Ml9erfeDY7xKlU=
golang.org/x/sync v0.0.0-20180314180146-1d60e4601c6f/go.mod h1:RxMgew5VJxzue5/jJTE5uejpjVlOe/izrB70Jof72aM=
golang.org/x/sync v0.0.0-20181108010431-42b317875d0f/go.mod h1:RxMgew5VJxzue5/jJTE5uejpjVlOe/izrB70Jof72aM=
golang.org/x/sync v0.0.0-20190423024810-112230192c58/go.mod h1:RxMgew5VJxzue5/jJTE5uejpjVlOe/izrB70Jof72aM=
golang.org/x/sync v0.0.0-20190911185100-cd5d95a43a6e/go.mod h1:RxMgew5VJxzue5/jJTE5uejpjVlOe/izrB70Jof72aM=
golang.org/x/sync v0.0.0-20201020160332-67f06af15bc9/go.mod h1:RxMgew5VJxzue5/jJTE5uejpjVlOe/izrB70Jof72aM=
golang.org/x/sync v0.0.0-20220722155255-886fb9371eb4/go.mod h1:RxMgew5VJxzue5/jJTE5uejpjVlOe/izrB70Jof72aM=
golang.org/x/sync v0.1.0/go.mod h1:RxMgew5VJxzue5/jJTE5uejpjVlOe/izrB70Jof72aM=
golang.org/x/sync v0.14.0 h1:woo0S4Yywslg6hp4eUFjTVOyKt0RookbpAHG4c1HmhQ=
golang.org/x/sync v0.14.0/go.mod h1:1dzgHSNfp02xaA81J2MS99Qcpr2w7fw1gpm99rleRqA=
golang.org/x/sys v0.0.0-20180830151530-49385e6e1522/go.mod h1:STP8DvDyc/dI5b8T5hshtkjS+E42TnysNCUPdjciGhY=
golang.org/x/sys v0.0.0-20190215142949-d0b11bdaac8a/go.mod h1:STP8DvDyc/dI5b8T5hshtkjS+E42TnysNCUPdjciGhY=
golang.org/x/sys v0.0.0-20190412213103-97732733099d/go.mod h1:h1NjWce9XRLGQEsW7wpKNCjG9DtNlClVuFLEZdDNbEs=
golang.org/x/sys v0.0.0-20190422165155-953cdadca894/go.mod h1:h1NjWce9XRLGQEsW7wpKNCjG9DtNlClVuFLEZdDNbEs=
golang.org/x/sys v0.0.0-20200930185726-fdedc70b468f/go.mod h1:h1NjWce9XRLGQEsW7wpKNCjG9DtNlClVuFLEZdDNbEs=
golang.org/x/sys v0.0.0-20201119102817-f84b799fce68/go.mod h1:h1NjWce9XRLGQEsW7wpKNCjG9DtNlClVuFLEZdDNbEs=
golang.org/x/sys v0.0.0-20210615035016-665e8c7367d1/go.mod h1:oPkhp1MJrh7nUepCBck5+mAzfO9JrbApNNgaTdGDITg=
golang.org/x/sys v0.0.0-20211025201205-69cdffdb9359/go.mod h1:oPkhp1MJrh7nUepCBck5+mAzfO9JrbApNNgaTdGDITg=
golang.org/x/sys v0.0.0-20220520151302-bc2c85ada10a/go.mod h1:oPkhp1MJrh7nUepCBck5+mAzfO9JrbApNNgaTdGDITg=
golang.org/x/sys v0.0.0-20220722155257-8c9f86f7a55f/go.mod h1:oPkhp1MJrh7nUepCBck5+mAzfO9JrbApNNgaTdGDITg=
golang.org/x/sys v0.5.0/go.mod h1:oPkhp1MJrh7nUepCBck5+mAzfO9JrbApNNgaTdGDITg=
golang.org/x/sys v0.6.0/go.mod h1:oPkhp1MJrh7nUepCBck5+mAzfO9JrbApNNgaTdGDITg=
golang.org/x/sys v0.8.0/go.mod h1:oPkhp1MJrh7nUepCBck5+mAzfO9JrbApNNgaTdGDITg=
golang.org/x/sys v0.13.0/go.mod h1:oPkhp1MJrh7nUepCBck5+mAzfO9JrbApNNgaTdGDITg=
golang.org/x/sys v0.33.0 h1:q3i8TbbEz+JRD9ywIRlyRAQbM0qF7hu24q3teo2hbuw=
golang.org/x/sys v0.33.0/go.mod h1:BJP2sWEmIv4KK5OTEluFJCKSidICx8ciO85XgH3Ak8k=
golang.org/x/term v0.0.0-20201126162022-7de9c90e9dd1/go.mod h1:bj7SfCRtBDWHUb9snDiAeCFNEtKQo2Wmx5Cou7ajbmo=
golang.org/x/term v0.0.0-20210927222741-03fcf44c2211/go.mod h1:jbD1KX2456YbFQfuXm/mYQcufACuNUgVhRMnK/tPxf8=
golang.org/x/term v0.5.0/go.mod h1:jMB1sMXY+tzblOD4FWmEbocvup2/aLOaQEp7JmGp78k=
golang.org/x/term v0.8.0/go.mod h1:xPskH00ivmX89bAKVGSKKtLOWNx2+17Eiy94tnKShWo=
golang.org/x/term v0.13.0/go.mod h1:LTmsnFJwVN6bCy1rVCoS+qHT1HhALEFxKncY3WNNh4U=
golang.org/x/term v0.32.0 h1:DR4lr0TjUs3epypdhTOkMmuF5CDFJ/8pOnbzMZPQ7bg=
golang.org/x/term v0.32.0/go.mod h1:uZG1FhGx848Sqfsq4/DlJr3xGGsYMu/L5GW4abiaEPQ=
golang.org/x/text v0.3.0/go.mod h1:NqM8EUOU14njkJ3fqMW+pc6Ldnwhi/IjpwHt7yyuwOQ=
golang.org/x/text v0.3.3/go.mod h1:5Zoc/QRtKVWzQhOtBMvqHzDpF6irO9z98xDceosuGiQ=
golang.org/x/text v0.3.7/go.mod h1:u+2+/6zg+i71rQMx5EYifcz6MCKuco9NR6JIITiCfzQ=
golang.org/x/text v0.3.8/go.mod h1:E6s5w1FMmriuDzIBO73fBruAKo1PCIq6d2Q6DHfQ8WQ=
golang.org/x/text v0.7.0/go.mod h1:mrYo+phRRbMaCq/xk9113O4dZlRixOauAjOtrjsXDZ8=
golang.org/x/text v0.9.0/go.mod h1:e1OnstbJyHTd6l/uOt8jFFHp6TRDWZR/bV3emEE/zU8=
golang.org/x/text v0.13.0/go.mod h1:TvPlkZtksWOMsz7fbANvkp4WM8x/WCo/om8BMLbz+aE=
golang.org/x/text v0.25.0 h1:qVyWApTSYLk/drJRO5mDlNYskwQznZmkpV2c8q9zls4=
golang.org/x/text v0.25.0/go.mod h1:WEdwpYrmk1qmdHvhkSTNPm3app7v4rsT8F2UD6+VHIA=
golang.org/x/time v0.11.0 h1:/bpjEDfN9tkoN/ryeYHnv5hcMlc8ncjMcM4XBk5NWV0=
golang.org/x/time v0.11.0/go.mod h1:CDIdPxbZBQxdj6cxyCIdrNogrJKMJ7pr37NYpMcMDSg=
golang.org/x/tools v0.0.0-20180917221912-90fa682c2a6e/go.mod h1:n7NCudcB/nEzxVGmLbDWY5pfWTLqBcC2KZ6jyYvM4mQ=
golang.org/x/tools v0.0.0-20190114222345-bf090417da8b/go.mod h1:n7NCudcB/nEzxVGmLbDWY5pfWTLqBcC2KZ6jyYvM4mQ=
golang.org/x/tools v0.0.0-20190226205152-f727befe758c/go.mod h1:9Yl7xja0Znq3iFh3HoIrodX9oNMXvdceNzlUR8zjMvY=
golang.org/x/tools v0.0.0-20190311212946-11955173bddd/go.mod h1:LCzVGOaR6xXOjkQ3onu1FJEFr0SW1gC7cKk1uF8kGRs=
golang.org/x/tools v0.0.0-20190524140312-2c0ae7006135/go.mod h1:RgjU9mgBXZiqYHBnxXauZ1Gv1EHHAz9KjViQ78xBX0Q=
golang.org/x/tools v0.0.0-20191108193012-7d206e10da11/go.mod h1:b+2E5dAYhXwXZwtnZ6UAqBI28+e2cm9otk0dWdXHAEo=
golang.org/x/tools v0.0.0-20191119224855-298f0cb1881e/go.mod h1:b+2E5dAYhXwXZwtnZ6UAqBI28+e2cm9otk0dWdXHAEo=
golang.org/x/tools v0.0.0-20200619180055-7c47624df98f/go.mod h1:EkVYQZoAsY45+roYkvgYkIh4xh/qjgUK9TdY2XT94GE=
golang.org/x/tools v0.0.0-20210106214847-113979e3529a/go.mod h1:emZCQorbCU4vsT4fOWvOPXz4eW1wZW4PmDk9uLelYpA=
golang.org/x/tools v0.1.12/go.mod h1:hNGJHUnrk76NpqgfD5Aqm5Crs+Hm0VOH/i9J2+nxYbc=
golang.org/x/tools v0.6.0/go.mod h1:Xwgl3UAJ/d3gWutnCtw505GrjyAbvKui8lOU390QaIU=
golang.org/x/tools v0.31.0 h1:0EedkvKDbh+qistFTd0Bcwe/YLh4vHwWEkiI0toFIBU=
golang.org/x/tools v0.31.0/go.mod h1:naFTU+Cev749tSJRXJlna0T3WxKvb1kWEx15xA4SdmQ=
golang.org/x/xerrors v0.0.0-20190717185122-a985d3407aa7/go.mod h1:I/5z698sn9Ka8TeJc9MKroUUfqBBauWjQqLJ2OPfmY0=
golang.org/x/xerrors v0.0.0-20191011141410-1b5146add898/go.mod h1:I/5z698sn9Ka8TeJc9MKroUUfqBBauWjQqLJ2OPfmY0=
golang.org/x/xerrors v0.0.0-20191204190536-9bdfabe68543/go.mod h1:I/5z698sn9Ka8TeJc9MKroUUfqBBauWjQqLJ2OPfmY0=
golang.org/x/xerrors v0.0.0-20200804184101-5ec99f83aff1/go.mod h1:I/5z698sn9Ka8TeJc9MKroUUfqBBauWjQqLJ2OPfmY0=
google.golang.org/api v0.232.0 h1:qGnmaIMf7KcuwHOlF3mERVzChloDYwRfOJOrHt8YC3I=
google.golang.org/api v0.232.0/go.mod h1:p9QCfBWZk1IJETUdbTKloR5ToFdKbYh2fkjsUL6vNoY=
google.golang.org/appengine v1.1.0/go.mod h1:EbEs0AVv82hx2wNQdGPgUI5lhzA/G0D9YwlJXL52JkM=
google.golang.org/appengine v1.4.0/go.mod h1:xpcJRLb0r/rnEns0DIKYYv+WjYCduHsrkT7/EB5XEv4=
google.golang.org/genproto v0.0.0-20180817151627-c66870c02cf8/go.mod h1:JiN7NxoALGmiZfu7CAH4rXhgtRTLTxftemlI0sWmxmc=
google.golang.org/genproto v0.0.0-20190819201941-24fa4b261c55/go.mod h1:DMBHOl98Agz4BDEuKkezgsaosCRResVns1a3J2ZsMNc=
google.golang.org/genproto v0.0.0-20200423170343-7949de9c1215/go.mod h1:55QSHmfGQM9UVYDPBsyGGes0y52j32PQ3BqQfXhyH3c=
google.golang.org/genproto v0.0.0-20200526211855-cb27e3aa2013/go.mod h1:NbSheEEYHJ7i3ixzK3sjbqSGDJWnxyFXZblF3eUsNvo=
google.golang.org/genproto v0.0.0-20250303144028-a0af3efb3deb h1:ITgPrl429bc6+2ZraNSzMDk3I95nmQln2fuPstKwFDE=
google.golang.org/genproto v0.0.0-20250303144028-a0af3efb3deb/go.mod h1:sAo5UzpjUwgFBCzupwhcLcxHVDK7vG5IqI30YnwX2eE=
google.golang.org/genproto/googleapis/api v0.0.0-20250313205543-e70fdf4c4cb4 h1:IFnXJq3UPB3oBREOodn1v1aGQeZYQclEmvWRMN0PSsY=
google.golang.org/genproto/googleapis/api v0.0.0-20250313205543-e70fdf4c4cb4/go.mod h1:c8q6Z6OCqnfVIqUFJkCzKcrj8eCvUrz+K4KRzSTuANg=
google.golang.org/genproto/googleapis/rpc v0.0.0-20250428153025-10db94c68c34 h1:h6p3mQqrmT1XkHVTfzLdNz1u7IhINeZkz67/xTbOuWs=
google.golang.org/genproto/googleapis/rpc v0.0.0-20250428153025-10db94c68c34/go.mod h1:qQ0YXyHHx3XkvlzUtpXDkS29lDSafHMZBAZDc03LQ3A=
google.golang.org/grpc v1.19.0/go.mod h1:mqu4LbDTu4XGKhr4mRzUsmM4RtVoemTSY81AxZiDr8c=
google.golang.org/grpc v1.23.0/go.mod h1:Y5yQAOtifL1yxbo5wqy6BxZv8vAUGQwXBOALyacEbxg=
google.golang.org/grpc v1.25.1/go.mod h1:c3i+UQWmh7LiEpx4sFZnkU36qjEYZ0imhYfXVyQciAY=
google.golang.org/grpc v1.27.0/go.mod h1:qbnxyOmOxrQa7FizSgH+ReBfzJrCY1pSN7KXBS8abTk=
google.golang.org/grpc v1.29.1/go.mod h1:itym6AZVZYACWQqET3MqgPpjcuV5QH3BxFS3IjizoKk=
google.golang.org/grpc v1.33.2/go.mod h1:JMHMWHQWaTccqQQlmk3MJZS+GWXOdAesneDmEnv2fbc=
google.golang.org/grpc v1.72.0 h1:S7UkcVa60b5AAQTaO6ZKamFp1zMZSU0fGDK2WZLbBnM=
google.golang.org/grpc v1.72.0/go.mod h1:wH5Aktxcg25y1I3w7H69nHfXdOG3UiadoBtjh3izSDM=
google.golang.org/protobuf v0.0.0-20200109180630-ec00e32a8dfd/go.mod h1:DFci5gLYBciE7Vtevhsrf46CRTquxDuWsQurQQe4oz8=
google.golang.org/protobuf v0.0.0-20200221191635-4d8936d0db64/go.mod h1:kwYJMbMJ01Woi6D6+Kah6886xMZcty6N08ah7+eCXa0=
google.golang.org/protobuf v0.0.0-20200228230310-ab0ca4ff8a60/go.mod h1:cfTl7dwQJ+fmap5saPgwCLgHXTUD7jkjRqWcaiX5VyM=
google.golang.org/protobuf v1.20.1-0.20200309200217-e05f789c0967/go.mod h1:A+miEFZTKqfCUM6K7xSMQL9OKL/b6hQv+e19PK+JZNE=
google.golang.org/protobuf v1.21.0/go.mod h1:47Nbq4nVaFHyn7ilMalzfO3qCViNmqZ2kzikPIcrTAo=
google.golang.org/protobuf v1.22.0/go.mod h1:EGpADcykh3NcUnDUJcl1+ZksZNG86OlYog2l/sGQquU=
google.golang.org/protobuf v1.23.0/go.mod h1:EGpADcykh3NcUnDUJcl1+ZksZNG86OlYog2l/sGQquU=
google.golang.org/protobuf v1.23.1-0.20200526195155-81db48ad09cc/go.mod h1:EGpADcykh3NcUnDUJcl1+ZksZNG86OlYog2l/sGQquU=
google.golang.org/protobuf v1.25.0/go.mod h1:9JNX74DMeImyA3h4bdi1ymwjUzf21/xIlbajtzgsN7c=
google.golang.org/protobuf v1.36.6 h1:z1NpPI8ku2WgiWnf+t9wTPsn6eP1L7ksHUlkfLvd9xY=
google.golang.org/protobuf v1.36.6/go.mod h1:jduwjTPXsFjZGTmRluh+L6NjiWu7pchiJ2/5YcXBHnY=
gopkg.in/check.v1 v0.0.0-20161208181325-20d25e280405/go.mod h1:Co6ibVJAznAaIkqp8huTwlJQCZ016jof/cbN4VW5Yz0=
gopkg.in/check.v1 v1.0.0-20180628173108-788fd7840127/go.mod h1:Co6ibVJAznAaIkqp8huTwlJQCZ016jof/cbN4VW5Yz0=
gopkg.in/check.v1 v1.0.0-20201130134442-10cb98267c6c h1:Hei/4ADfdWqJk1ZMxUNpqntNwaWcugrBjAiHlqqRiVk=
gopkg.in/check.v1 v1.0.0-20201130134442-10cb98267c6c/go.mod h1:JHkPIbrfpd72SG/EVd6muEfDQjcINNoR0C8j2r3qZ4Q=
gopkg.in/yaml.v2 v2.2.2/go.mod h1:hI93XBmqTisBFMUTm0b8Fm+jr3Dg1NNxqwp+5A1VGuI=
gopkg.in/yaml.v2 v2.2.8/go.mod h1:hI93XBmqTisBFMUTm0b8Fm+jr3Dg1NNxqwp+5A1VGuI=
gopkg.in/yaml.v3 v3.0.0-20200313102051-9f266ea9e77c/go.mod h1:K4uyk7z7BCEPqu6E+C64Yfv1cQ7kz7rIZviUmN+EgEM=
gopkg.in/yaml.v3 v3.0.0-20210107192922-496545a6307b/go.mod h1:K4uyk7z7BCEPqu6E+C64Yfv1cQ7kz7rIZviUmN+EgEM=
gopkg.in/yaml.v3 v3.0.1 h1:fxVm/GzAzEWqLHuvctI91KS9hhNmmWOoWu0XTYJS7CA=
gopkg.in/yaml.v3 v3.0.1/go.mod h1:K4uyk7z7BCEPqu6E+C64Yfv1cQ7kz7rIZviUmN+EgEM=
honnef.co/go/tools v0.0.0-20190102054323-c2f93a96b099/go.mod h1:rf3lG4BRIbNafJWhAfAdb/ePZxsR/4RtNHQocxwk9r4=
honnef.co/go/tools v0.0.0-20190523083050-ea95bdfd59fc/go.mod h1:rf3lG4BRIbNafJWhAfAdb/ePZxsR/4RtNHQocxwk9r4=
modernc.org/cc/v4 v4.25.2 h1:T2oH7sZdGvTaie0BRNFbIYsabzCxUQg8nLqCdQ2i0ic=
modernc.org/cc/v4 v4.25.2/go.mod h1:uVtb5OGqUKpoLWhqwNQo/8LwvoiEBLvZXIQ/SmO6mL0=
modernc.org/ccgo/v4 v4.25.1 h1:TFSzPrAGmDsdnhT9X2UrcPMI3N/mJ9/X9ykKXwLhDsU=
modernc.org/ccgo/v4 v4.25.1/go.mod h1:njjuAYiPflywOOrm3B7kCB444ONP5pAVr8PIEoE0uDw=
modernc.org/fileutil v1.3.0 h1:gQ5SIzK3H9kdfai/5x41oQiKValumqNTDXMvKo62HvE=
modernc.org/fileutil v1.3.0/go.mod h1:XatxS8fZi3pS8/hKG2GH/ArUogfxjpEKs3Ku3aK4JyQ=
modernc.org/gc/v2 v2.6.5 h1:nyqdV8q46KvTpZlsw66kWqwXRHdjIlJOhG6kxiV/9xI=
modernc.org/gc/v2 v2.6.5/go.mod h1:YgIahr1ypgfe7chRuJi2gD7DBQiKSLMPgBQe9oIiito=
modernc.org/libc v1.62.1 h1:s0+fv5E3FymN8eJVmnk0llBe6rOxCu/DEU+XygRbS8s=
modernc.org/libc v1.62.1/go.mod h1:iXhATfJQLjG3NWy56a6WVU73lWOcdYVxsvwCgoPljuo=
modernc.org/mathutil v1.7.1 h1:GCZVGXdaN8gTqB1Mf/usp1Y/hSqgI2vAGGP4jZMCxOU=
modernc.org/mathutil v1.7.1/go.mod h1:4p5IwJITfppl0G4sUEDtCr4DthTaT47/N3aT6MhfgJg=
modernc.org/memory v1.9.1 h1:V/Z1solwAVmMW1yttq3nDdZPJqV1rM05Ccq6KMSZ34g=
modernc.org/memory v1.9.1/go.mod h1:/JP4VbVC+K5sU2wZi9bHoq2MAkCnrt2r98UGeSK7Mjw=
modernc.org/opt v0.1.4 h1:2kNGMRiUjrp4LcaPuLY2PzUfqM/w9N23quVwhKt5Qm8=
modernc.org/opt v0.1.4/go.mod h1:03fq9lsNfvkYSfxrfUhZCWPk1lm4cq4N+Bh//bEtgns=
modernc.org/sortutil v1.2.1 h1:+xyoGf15mM3NMlPDnFqrteY07klSFxLElE2PVuWIJ7w=
modernc.org/sortutil v1.2.1/go.mod h1:7ZI3a3REbai7gzCLcotuw9AC4VZVpYMjDzETGsSMqJE=
modernc.org/sqlite v1.37.0 h1:s1TMe7T3Q3ovQiK2Ouz4Jwh7dw4ZDqbebSDTlSJdfjI=
modernc.org/sqlite v1.37.0/go.mod h1:5YiWv+YviqGMuGw4V+PNplcyaJ5v+vQd7TQOgkACoJM=
modernc.org/strutil v1.2.1 h1:UneZBkQA+DX2Rp35KcM69cSsNES9ly8mQWD71HKlOA0=
modernc.org/strutil v1.2.1/go.mod h1:EHkiggD70koQxjVdSBM3JKM7k6L0FbGE5eymy9i3B9A=
modernc.org/token v1.1.0 h1:Xl7Ap9dKaEs5kLoOQeQmPWevfnk/DM5qcLcYlA8ys6Y=
modernc.org/token v1.1.0/go.mod h1:UGzOrNV1mAFSEB63lOFHIpNRUVMvYTc6yu1SMY/XTDM=

>>>> infrastructure/Dockerfile
# syntax=docker/dockerfile:1

FROM golang:1.24.5-alpine

WORKDIR /usr/src/app

COPY go.mod go.sum ./
RUN go mod tidy && go mod download && go mod verify

COPY . .
RUN go build -v -o /usr/local/bin/app cmd/web/*.go

CMD ["app"]

>>>> infrastructure/cloudbuild.yaml
steps:

  - name: "gcr.io/cloud-builders/docker"
    id: "docker-pull"
    entrypoint: "bash"
    args:
      - "-c"
      - "docker pull europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:latest || exit 0"
    waitFor: ["-"]

  ## use the global $SHORT_SHA for a rollout rollback strategy
  - name: "gcr.io/cloud-builders/docker"
    id: "docker-build"
    args:
      [
        "build",
        "-t",
        "europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:latest",
        "-t",
        "europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:$SHORT_SHA",
        "--build-arg=_PRJENV=$_PRJENV",
        "--cache-from",
        "europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:latest",
        "-f",
        "infrastructure/Dockerfile",
        ".",
      ]

  - name: "gcr.io/cloud-builders/docker"
    id: "docker-push-sha"
    args:
      [
        "push",
        "europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:$SHORT_SHA",
      ]

  - name: "gcr.io/cloud-builders/docker"
    id: "docker-push-lastest"
    args:
      [
        "push",
        "europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:latest",
      ]

  - name: "gcr.io/cloud-builders/kubectl"
    id: "kubectl-get-credentials"
    entrypoint: "sh"
    args:
      - "-c"
      - 'gcloud container clusters get-credentials --region "$_REGION" "sd-a01"'
    waitFor: ["docker-push-lastest"]

  - name: "gcr.io/cloud-builders/kubectl"
    id: "kubectl-set-image"
    entrypoint: "sh"
    args:
      - "-c"
      - |
        kubectl --namespace=$_PRJENV set image deployment/shortlink shortlink=europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:$SHORT_SHA \
        && kubectl --namespace=$_PRJENV annotate deployment/shortlink kubernetes.io/change-cause="image updated to $SHORT_SHA"
    waitFor: ["kubectl-get-credentials", "docker-push-sha"]

  - name: "gcr.io/cloud-builders/kubectl"
    id: "kubectl-set-version"
    entrypoint: "sh"
    args:
      - "-c"
      - 'kubectl --namespace=$_PRJENV set env deployment/shortlink VERSION="$SHORT_SHA"'
    waitFor: ["kubectl-get-credentials", "kubectl-set-image"]

images:
  - "europe-west3-docker.pkg.dev/$_PRJENV/shortlink-service/shortlink-service:$SHORT_SHA"

# ╔════════════════════════════════════════════════════════════════════════════╗
# ║ Options                                                                    ║
# ╚════════════════════════════════════════════════════════════════════════════╝

options:
  machineType: "E2_HIGHCPU_8"
  logging: "CLOUD_LOGGING_ONLY"

timeout: "1600s"

>>>> migrations/20241218153000_create_link_table.go
package migrations

import "gofr.dev/pkg/gofr/migration"

const createTable = `CREATE TABLE IF NOT EXISTS link
(
	id             UUID            NOT NULL DEFAULT gen_random_uuid()
	PRIMARY KEY,
    type           varchar(16) NOT NULL,
    path           varchar(48)  NOT NULL,
    original_url   varchar(2000) NOT NULL
);`

func createTableLink() migration.Migrate {
	return migration.Migrate{
		UP: func(d migration.Datasource) error {
			_, err := d.SQL.Exec(createTable)
			if err != nil {
				return err
			}
			return nil
		},
	}
}

>>>> migrations/20241218153001_alter_link_table.go
package migrations

import "gofr.dev/pkg/gofr/migration"

const alterTable = `ALTER TABLE shortlink.link
    ALTER COLUMN type TYPE varchar(32),
    ALTER COLUMN path TYPE varchar(128),
    ALTER COLUMN original_url TYPE varchar(2048);`

func alterTableLink() migration.Migrate {
	return migration.Migrate{
		UP: func(d migration.Datasource) error {
			_, err := d.SQL.Exec(alterTable)
			if err != nil {
				return err
			}
			return nil
		},
	}
}

>>>> migrations/20250718105740_firebase_link_handler.go
package migrations

import (
	"fmt"

	"gofr.dev/pkg/gofr/migration"
)

func FirebaseLinkHandler() migration.Migrate {
	return migration.Migrate{
		UP: func(d migration.Datasource) error {
			// Add logging to debug migration execution
			fmt.Println("FirebaseLinkHandler migration: Starting migration execution")

			queries := []string{
				`ALTER TABLE shortlink.link ADD COLUMN IF NOT EXISTS src_url VARCHAR(2048);`,
				`ALTER TABLE shortlink.link ADD COLUMN IF NOT EXISTS target_url VARCHAR(2048);`,
			}

			for i, query := range queries {
				fmt.Printf("FirebaseLinkHandler migration: Executing query %d: %s\n", i+1, query)
				_, err := d.SQL.Exec(query)
				if err != nil {
					fmt.Printf("FirebaseLinkHandler migration: Error executing query %d: %v\n", i+1, err)
					return err
				}
				fmt.Printf("FirebaseLinkHandler migration: Successfully executed query %d\n", i+1)
			}

			fmt.Printf("FirebaseLinkHandler migration: Migration completed successfully")
			return nil
		},
	}
}

>>>> migrations/20250718113142_add_timestamps.go
package migrations

import (
	"fmt"

	"gofr.dev/pkg/gofr/migration"
)

func AddTimestamps() migration.Migrate {

	return migration.Migrate{
		UP: func(d migration.Datasource) error {
			fmt.Println("AddTimestamps migration: Starting migration execution")

			queries := []string{
				`ALTER TABLE shortlink.link ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL;`,
				`ALTER TABLE shortlink.link ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP NULL;`,
				`ALTER TABLE shortlink.link ADD COLUMN IF NOT EXISTS inserted_at TIMESTAMP NULL;`,
			}

			for i, query := range queries {
				fmt.Printf("AddTimestamps migration: Executing query %d: %s\n", i+1, query)
				_, err := d.SQL.Exec(query)
				if err != nil {
					fmt.Printf("AddTimestamps migration: Error executing query %d: %v\n", i+1, err)
					return err
				}
				fmt.Printf("AddTimestamps migration: Successfully executed query %d\n", i+1)
			}

			fmt.Printf("AddTimestamps migration: Migration completed successfully")
			return nil
		},
	}
}

>>>> migrations/20250718114341_add_tracking_impressions_counter.go
package migrations

import (
	"fmt"

	"gofr.dev/pkg/gofr/migration"
)

func AddTrackingImpressionsCounter() migration.Migrate {
	return migration.Migrate{
		UP: func(d migration.Datasource) error {

			fmt.Println("AddTrackingImpressionsCounter migration: Starting migration execution")

			queries := []string{
				`ALTER TABLE shortlink.link ADD COLUMN IF NOT EXISTS tracking_impressions_counter INTEGER DEFAULT 0;`,
			}

			for i, query := range queries {
				fmt.Printf("AddTrackingImpressionsCounter migration: Executing query %d: %s\n", i+1, query)
				_, err := d.SQL.Exec(query)
				if err != nil {
					fmt.Printf("AddTrackingImpressionsCounter migration: Error executing query %d: %v\n", i+1, err)
					return err
				}
				fmt.Printf("AddTrackingImpressionsCounter migration: Successfully executed query %d\n", i+1)
			}

			fmt.Printf("AddTrackingImpressionsCounter migration: Migration completed successfully")
			return nil

		},
	}
}

>>>> migrations/20250721184420_remove_constraint_from_type.go
package migrations

import (
	"gofr.dev/pkg/gofr/migration"
)

func RemoveConstraintFromType() migration.Migrate {
	return migration.Migrate{
		UP: func(d migration.Datasource) error {
			// write your migrations here
			query := "ALTER TABLE shortlink.link ALTER COLUMN \"type\" DROP NOT NULL;"
			query = query + "ALTER TABLE shortlink.link ALTER COLUMN \"path\" DROP NOT NULL;"
			query = query + "ALTER TABLE shortlink.link ALTER COLUMN \"original_url\" DROP NOT NULL;"
			_, err := d.SQL.Exec(query)
			if err != nil {
				return err
			}
			return nil
		},
	}
}

>>>> migrations/README.md
# This Folder

This folder containes gofr.dev's database migration files in the first place. For more details on naming conventions and usage, please visit https://gofr.dev/docs/advanced-guide/handling-data-migrations

>>>> migrations/all.go
// This is auto-generated file using 'gofr migrate' tool. DO NOT EDIT.
package migrations

import (
	"gofr.dev/pkg/gofr/migration"
)

func All() map[int64]migration.Migrate {
	return map[int64]migration.Migrate{

		20241218153000: createTableLink(),
		20241218153001: alterTableLink(),
		20250718105740: FirebaseLinkHandler(),
		20250718113142: AddTimestamps(),
		20250718114341: AddTrackingImpressionsCounter(),
		20250721184420: RemoveConstraintFromType(),
	}
}

>>>> pkg/config/config.go
package config

import (
	"html/template"
	"log"

	"github.com/alexedwards/scs/v2"
)

// AppConfig holds the application config
type AppConfig struct {
	UseCache               bool
	TemplateCache          map[string]*template.Template
	InfoLog                *log.Logger
	InProduction           bool
	Session                *scs.SessionManager
	EventsServiceAvailable bool
	// TODO: add flag for telemetry service available
	// TODO: add flag for pubsub service available
}

>>>> pkg/handlers/event_redirection_handler.go
package handlers

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/url"
	"time"

	"gofr.dev/pkg/gofr"
	"gofr.dev/pkg/gofr/container"
	"gofr.dev/pkg/gofr/http/response"
)

const (
	defaultDbTimeout       = time.Duration(5) * time.Second
	queryRetrieveTargetUrl = "SELECT original_url FROM link WHERE id = $1"
)

var (
	ErrShortLinkNotFound           = errors.New("short link not found")
	ErrShortLinkIdentifierNotFound = errors.New("short link identifier not found in database")
	ErrFailedToRetrieveTargetUrl   = errors.New("failed to retrieve target URL")
	ErrInvalidUrl                  = errors.New("invalid URL format")
)

func EventRedirectionHandler(ctx *gofr.Context) (interface{}, error) {
	identifier := ctx.PathParam("identifier")
	if identifier == "" {
		return nil, fmt.Errorf("identifier parameter is required")
	}

	fmt.Printf("Processing redirection for identifier: %s\n", identifier)

	queryCtx, cancel := context.WithTimeout(context.Background(), defaultDbTimeout)
	defer cancel()

	var targetUrl string
	targetUrl, err := retrieveOriginalURL(queryCtx, ctx.SQL, identifier)
	if err != nil {
		return nil, err
	}

	if err := validateURL(targetUrl); err != nil {
		return nil, err
	}

	ctx.Logger.Info("Successfully redirected to URL", "original_url", targetUrl)

	return response.Redirect{
		URL: targetUrl,
	}, nil
}

func retrieveOriginalURL(ctx context.Context, db container.DB, identifier string) (string, error) {
	var originalUrl string // DEPRECATED: use SrcURL instead

	if err := db.QueryRowContext(ctx, queryRetrieveTargetUrl, identifier).Scan(&originalUrl); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return "", ErrShortLinkIdentifierNotFound
		}
		return "", ErrFailedToRetrieveTargetUrl
	}

	if originalUrl == "" {
		return "", ErrShortLinkNotFound
	}

	return originalUrl, nil
}

func validateURL(urlStr string) error {
	parsedUrl, err := url.Parse(urlStr)

	if err != nil || parsedUrl.Scheme == "" || parsedUrl.Host == "" {
		return ErrInvalidUrl
	}

	return nil
}

>>>> pkg/handlers/firebase_link_creation_handler.go
package handlers

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/stagedates/shortlink-service/pkg/validators"
	"gofr.dev/pkg/gofr"
)

type DynamicLink struct {
	ID         uuid.UUID `json:"id"`
	SrcURL     string    `json:"shortlink"`
	TargetURL  string    `json:"target_url"`
	InsertedAt time.Time `json:"inserted_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// FirebaseLinkCreationHandler creates a new link in the database
// based on the firebase dynamic link specification found here:
// https://firebase.google.com/docs/dynamic-links/rest
// Target url example "https://stagedates.com/events/star-fm-rooftop-party-nhow-berlin-20250726-vDO1Q"
func FirebaseLinkCreationHandler(app *gofr.App) func(*gofr.Context) (any, error) {
	return func(ctx *gofr.Context) (any, error) {
		var (
			link      DynamicLink
			_srcURL   string
			srcURL    string
			targetUrl string
		)

		if err := ctx.Bind(&link); err != nil {
			return nil, err
		}

		ctx.Logger.Debugf("inserting link: %+v", link)

		// Extract API key from context
		requestApiKeyRaw := ctx.Value("RequestApiKey")

		// Convert to string safely
		requestApiKey := ""
		if requestApiKeyRaw != nil {
			if apiKeyStr, ok := requestApiKeyRaw.(string); ok {
				requestApiKey = apiKeyStr
			}
		}

		if !validators.ApiKeyValidator(app, requestApiKey) {
			ctx.Logger.Warn("Unauthorized: Invalid or missing API key")
			return nil, errors.New("unauthorized: invalid or missing API key")
		}

		// the source_url is the shortlink that the user will be redirected from and might prefer during creation
		if len(link.SrcURL) > 0 {
			_srcURL = link.SrcURL
		} else {
			_srcURL = app.Config.GetOrDefault("SHORTLINK_BASE_URL", "https://stagedates.com/")
		}

		srcURL = _srcURL
		targetUrl = link.TargetURL
		insertedAt := time.Now()
		updatedAt := time.Time{}

		query := "INSERT INTO link (src_url, target_url, inserted_at, updated_at) "
		query = query + "VALUES ($1, $2, $3, $4) "

		_, err := ctx.SQL.ExecContext(ctx,
			query,
			srcURL, targetUrl, insertedAt, updatedAt)

		if err != nil {
			ctx.Logger.Errorf("error inserting link: %+v", err)
			return nil, err
		} else {
			ctx.Logger.Infof("link inserted: %+v", link)
			return "ok", nil
		}

	}
}

>>>> pkg/handlers/firebase_link_handler.go
package handlers

import (
	"errors"

	"gofr.dev/pkg/gofr"
	"gofr.dev/pkg/gofr/http/response"
)

const fallbackUrl = "https://stagedates.com"

var (
	ErrDbResultsNotFound                       = errors.New("FirebaseLinkHandler(): No results found - failed to retrieve target URL")
	ErrShortLinkIdentifierUrlParameterNotFound = errors.New("FirebaseLinkHandler(): short link identifier url parameter required but not found")
	fallbackRedirect                           = response.Redirect{
		URL: fallbackUrl,
	}
)

type FirebaseLink struct {
	ID          string `json:"id"`
	OriginalURL string `json:"original_url"` // DEPRECATED: use SrcURL instead
}

// FirebaseLinkHandler handles requests to the root (/) route for Firebase links
func FirebaseLinkHandler(ctx *gofr.Context) (interface{}, error) {
	// Get the identifier from the URL path
	identifier := ctx.PathParam("identifier")

	if identifier == "" {
		ctx.Logf(ErrShortLinkIdentifierUrlParameterNotFound.Error())
		return ErrShortLinkIdentifierUrlParameterNotFound, nil
	}

	if identifier == "health" {
		return "ok", nil
	}

	query := "SELECT id, original_url FROM link WHERE path = $1"
	rows, err := ctx.SQL.QueryContext(ctx, query, identifier)
	defer rows.Close()

	if err != nil {
		ctx.Logf("FirebaseLinkHandler(): %v", err)
		return ErrDbResultsNotFound, nil
	}

	var results []FirebaseLink
	for rows.Next() {
		var link FirebaseLink
		if err := rows.Scan(&link.ID, &link.OriginalURL); err != nil {
			ctx.Logf("FirebaseLinkHandler(): %v", err)
			return fallbackRedirect, nil
		}
		results = append(results, link)
		ctx.Logf("FirebaseLinkHandler(): appending: %v", link)
		break // Only process the first row since we expect only one result
	}

	if len(results) > 0 {
		targetUrl := results[0].OriginalURL

		if err := validateURL(targetUrl); err != nil {
			ctx.Logf(ErrInvalidUrl.Error())
			return ErrInvalidUrl, err
		}

		ctx.Logf("FirebaseLinkHandler(): Redirecting to URL", "original_url", targetUrl)
		return response.Redirect{
			URL: targetUrl,
		}, nil
	} else {
		ctx.Logf(ErrDbResultsNotFound.Error())
		return ErrDbResultsNotFound, nil
	}
}

>>>> pkg/handlers/handlers.go
package handlers

import (
	"net/http"

	"github.com/stagedates/shortlink-service/pkg/config"
	"github.com/stagedates/shortlink-service/pkg/models"
	"github.com/stagedates/shortlink-service/pkg/render"
)

// Repo the repository used by the handlers
var Repo *Repository

// Repository is the repository type
type Repository struct {
	App *config.AppConfig
}

// NewRepo creates a new repository
func NewRepo(a *config.AppConfig) *Repository {
	return &Repository{
		App: a,
	}
}

// NewHandlers sets the repository for the handlers
func NewHandlers(r *Repository) {
	Repo = r
}

// Home is the handler for the home page
func (m *Repository) Home(w http.ResponseWriter, r *http.Request) {
	remoteIP := r.RemoteAddr
	m.App.Session.Put(r.Context(), "remote_ip", remoteIP)

	render.WriteTemplate(w, "home.page.tmpl", &models.TemplateData{})
}

// About is the handler for the about page
func (m *Repository) About(w http.ResponseWriter, r *http.Request) {
	// perform some logic
	stringMap := make(map[string]string)
	stringMap["test"] = "Hello, again"

	remoteIP := m.App.Session.GetString(r.Context(), "remote_ip")
	stringMap["remote_ip"] = remoteIP

	// send data to the template
	render.WriteTemplate(w, "about.page.tmpl", &models.TemplateData{
		StringMap: stringMap,
	})
}

// Gofr Page
func (m *Repository) Gofr(w http.ResponseWriter, r *http.Request) {
	// perform some logic
	stringMap := make(map[string]string)
	stringMap["test"] = "Hello, gofr"

	// send data to the template
	render.WriteTemplate(w, "gofr.page.tmpl", &models.TemplateData{
		StringMap: stringMap,
	})
}

>>>> pkg/handlers/kubernetes_health.go
package handlers

import (
	"gofr.dev/pkg/gofr"
)

func HealthHandler(ctx *gofr.Context) (interface{}, error) {
	return map[string]string{"status": "ok"}, nil

	// var link Link
	// if err := ctx.Bind(&link); err != nil {
	// 	return nil, err
	// }

	// return "ok", nil
}

>>>> pkg/handlers/opengraph.go
package handlers

import (
	"fmt"
)

func generateOGTags(title, description, imageURL, pageURL string) string {
	return fmt.Sprintf(`
        <meta property="og:title" content="%s" />
        <meta property="og:description" content="%s" />
        <meta property="og:image" content="%s" />
        <meta property="og:url" content="%s" />
        <meta property="og:type" content="website" />
    `, title, description, imageURL, pageURL)
}

// OG is the handler for the open graph page
// func (m *Repository) OpenGraph(w http.ResponseWriter, r *http.Request) {
func OpenGraph(title, description, imageURL, pageURL string) string {

	// stringMap := make(map[string]string)
	// stringMap["test"] = "CHANGEME"

	////////////////// ^

	// Get the current page URL
	//pageURL := "https://website.com" + r.URL.Path

	// Generate or get the image URL for this page
	//imageURL := "https://website.com/images/og-image.jpg" // TODO: set current url

	// Generate Open Graph tags
	ogTags := generateOGTags(title, description, imageURL, pageURL)

	// Set the content type
	//w.Header().Set("Content-Type", "text/html; charset=utf-8")

	// Write the HTML response with Open Graph tags
	//fmt.Fprintf(w, `
	return fmt.Sprintf(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>%s</title>
        %s
    </head>
    <body>
        <h1>%s</h1>
    </body>
    </html>
    `, title, ogTags, title)

	////////////////// $

	//render.RenderTemplate(w, "opengraph.partial.tmpl", &models.TemplateData{
	//	StringMap: stringMap,
	//})
}

>>>> pkg/handlers/preview_link_handlers.go
package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"regexp"

	"github.com/stagedates/shortlink-service/pkg/serviceclients"
	"gofr.dev/pkg/gofr"
)

var uuidRegex = regexp.MustCompile(`^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`)

type Event struct {
	ID            string `json:"id"`
	EventID       string `json:"event_id"`
	Title         string `json:"title"`
	Subtitle      string `json:"subtitle"`
	Description   string `json:"description"`
	ShortCode     string `json:"shortCode"`
	ThumbnailURL  string `json:"thumbnailUrl"`
	CoverURL      string `json:"coverUrl"`
	Slug          string `json:"slug"`
	ThumbnailPath string `json:"thumbnailPath"`
	IsDraft       bool   `json:"isDraft"`
}

type EventClient interface {
	GetEvent(*gofr.App, string) (*Event, error)
}

type RawResponse struct {
	Content string `json:"content"`
	Type    string `json:"type"`
}

func (r RawResponse) MarshalJSON() ([]byte, error) {
	// Create a map to hold the content
	data := map[string]interface{}{
		"data": map[string]string{
			"content": r.Content,
			"type":    r.Type,
		},
	}
	// Marshal the map to JSON
	return json.Marshal(data)
}

func (r RawResponse) GetContentType() string {
	return r.Type
}

// EventPreviewLinkHandler is a handler for event preview links
// It generates the html document for the event preview
func EventPreviewLinkHandler(app *gofr.App) func(*gofr.Context) (interface{}, error) {
	return func(c *gofr.Context) (interface{}, error) {
		identifier := c.PathParam("identifier")
		if identifier == "" {
			return nil, fmt.Errorf("identifier parameter is required")
		}

		event, err := ProcessEventIdentifier(app, identifier)
		if err != nil {
			return nil, err
		}

		// Cast the event to the correct type
		eventData, ok := event.(*Event)
		if !ok {
			return nil, fmt.Errorf("invalid event data type")
		}

		// Generate the OpenGraph HTML
		html := OpenGraph(
			eventData.Title,
			eventData.Description,
			eventData.ThumbnailURL,
			eventData.Slug,
		)

		// Return a raw response
		return RawResponse{
			Content: html,
			Type:    "text/html",
		}.Content, nil
	}
}

func ProcessEventIdentifier(app *gofr.App, identifier string) (interface{}, error) {
	if isUUID(identifier) {
		return processEventByUUID(app, identifier)
	}
	return processEventBySlug(app, identifier)
}

// Helper functions (implement these based on your needs)
func isUUID(str string) bool {
	return uuidRegex.MatchString(str)
}

func processEventByUUID(app *gofr.App, uuid string) (interface{}, error) {
	app.Logger().Infof("Processing event with UUID: %s", uuid)

	// Get event from events service
	eventsClient, err := serviceclients.NewEventsClient(app.Config.GetOrDefault("EVENTS_SERVICE_BASE_URL", "http://localhost:8080"))
	event, err := eventsClient.GetEvent(app, uuid)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch event: %v", err)
	}

	return event, nil
}

func processEventBySlug(app *gofr.App, slug string) (interface{}, error) {
	log.Printf("Processing event with slug: %s", slug)

	eventsClient, err := serviceclients.NewEventsClient(app.Config.GetOrDefault("EVENTS_SERVICE_BASE_URL", "http://localhost:8080"))

	// Get event from events service
	event, err := eventsClient.GetEvent(app, slug)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch event: %v", err)
	}

	return event, nil
}

// TODO:
func main_event_processing_from_web(eventsClient interface{}, gofrapp *gofr.App, msg []byte) {
	var payload struct {
		Data struct {
			ID string `json:"id"`
		} `json:"data"`
	}

	if err := json.Unmarshal(msg, &payload); err == nil && payload.Data.ID != "" {
		gofrapp.Logger().Infof("ID: %s\n", payload.Data.ID)

		// Fetch event details using GetEvent
		event, err := eventsClient.(interface {
			GetEvent(*gofr.App, string) (*Event, error)
		}).GetEvent(gofrapp, payload.Data.ID)
		if err != nil {
			gofrapp.Logger().Errorf("Error getting event: %v", err)
		} else {
			gofrapp.Logger().Infof("\n--- Event Details ---")
			gofrapp.Logger().Infof("Title: %s", event.Title)
			gofrapp.Logger().Infof("Subtitle: %s", event.Subtitle)
			gofrapp.Logger().Infof("Description: %s", event.Description)
			gofrapp.Logger().Infof("Thumbnail URL: %s", event.ThumbnailURL)
			gofrapp.Logger().Infof("Cover URL: %s", event.CoverURL)
			gofrapp.Logger().Infof("Slug: %s", event.Slug)
			gofrapp.Logger().Infof("Thumbnail Path: %s", event.ThumbnailPath)
			gofrapp.Logger().Infof("Is Draft: %v", event.IsDraft)

			gofrapp.Logger().Infof(OpenGraph(event.Title, event.Description, event.ThumbnailURL, event.Slug))
		}
	}
}

>>>> pkg/handlers/pubsub.go
// https://gofr.dev/docs/advanced-guide/using-publisher-subscriber#example
package handlers

import (
	"fmt"

	"cloud.google.com/go/pubsub"
	"gofr.dev/pkg/gofr"

	"context"
	"time"
)

//var HandleMessagesWithGooglesPubSubClient func(gofrapp *gofr.App, ctx context.Context, subscriptionID, projectID string) error

func StartGooglesEventPubSubProcessor(gofrapp *gofr.App) error {

	projectID := "stdts-dev"
	topicId := "events.events"
	subID := "shortlink-worker.events.events"

	ctx := context.Background()

	// Creates a client.
	client, err := pubsub.NewClient(ctx, projectID)
	if err != nil {
		gofrapp.Logger().Fatalf("Failed to create client: %v", err)
	}

	topic := client.Topic(topicId)
	defer client.Close()

	client.CreateSubscription(ctx, subID, pubsub.SubscriptionConfig{
		Topic:       topic,
		AckDeadline: 20 * time.Second,
	})

	if err != nil {
		gofrapp.Logger().Errorf("CreateSubscription: %w", err)
	}

	return nil
}

// handleMessage processes incoming Pub/Sub messages.
func HandleMessagesWithGooglesPubSubClient(gofrapp *gofr.App, ctx context.Context, subscriptionID, projectID string, msgHandler func([]byte)) error {
	client, err := pubsub.NewClient(ctx, projectID)
	if err != nil {
		return fmt.Errorf("pubsub.NewClient: %v", err)
	}
	defer client.Close()

	sub := client.Subscription(subscriptionID)
	err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
		msgHandler(msg.Data)
		msg.Ack()
	})

	return err
}

/*
func HandleMessagesWithGooglesPubSubClient(gofrapp *gofr.App, ctx context.Context, subscriptionID, projectID string) error {
	gofrapp.Logger().Info("HandleMessagesWithGooglesPubSubClient(): starting ...")
	client, err := pubsub.NewClient(ctx, projectID)
	if err != nil {
		gofrapp.Logger().Errorf("pubsub.NewClient: %w", err)
	}
	defer client.Close()

	sub := client.Subscription(subscriptionID)

	err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
		gofrapp.Logger().Infof("\n--- Message received at %v ---\n", time.Now().Format(time.RFC3339))
		gofrapp.Logger().Infof("Data: %s\n", string(msg.Data))

		// Parse message as JSON to check for ID
		// and pass it to the opengraph handler
		var payload struct {
			Data struct {
				ID          string `json:"id"`
				Title       string `json:"title"`
				Description string `json:"description"`
				ImageURL    string `json:"image_url"`
				PageURL     string `json:"page_url"`
			} `json:"data"`
		}

		if err := json.Unmarshal(msg.Data, &payload); err == nil {
			if payload.Data.ID != "" {
				gofrapp.Logger().Infof("ID: %s\n", payload.Data.ID)
			}

			// Generate OpenGraph tags if we have the required fields
			ogTags := generateOGTags(
				payload.Data.Title,
				payload.Data.Description,
				payload.Data.ImageURL,
				payload.Data.PageURL,
			)

			gofrapp.Logger().Infof("Generated OpenGraph Tags:\n%s", ogTags)
		}

		if len(msg.Attributes) > 0 {
			gofrapp.Logger().Info("Attributes:")
			for key, value := range msg.Attributes {
				gofrapp.Logger().Infof("  %s: %s\n", key, value)
			}
		}
		gofrapp.Logger().Infof("PublishTime: %v\n", msg.PublishTime)
		gofrapp.Logger().Infof("Message ID: %s\n", msg.ID)
		msg.Ack()
	})

	if err != nil {
		return fmt.Errorf("sub.Receive: %w", err)
	}

	gofrapp.Logger().Info("HandleMessagesWithGooglesPubSubClient(): leaving ...")
	return nil
} */

// DEPR - Optional implementation of the Pub/Sub subscriber
func StartGofrEventPubSubProcessor(gofrapp *gofr.App) {
	topic := "events.events"
	gofrapp.Subscribe(topic, func(c *gofr.Context) error {
		var orderStatus struct {
			OrderId string `json:"orderId"`
			Status  string `json:"status"`
		}

		err := c.Bind(&orderStatus)
		if err != nil {
			c.Logger.Error(err)

			// returning nil here as we would like to ignore the
			// incompatible message and continue reading forward
			return nil
		}

		c.Logger.Info("Received order ", orderStatus)

		return nil
	})
}

>>>> pkg/models/templatedata.go
package models

// TemplateData holds data sent from handlers to templates
type TemplateData struct {
	StringMap map[string]string
	IntMap    map[string]int
	FloatMap  map[string]float32
	Data      map[string]interface{}
	CSRFToken string
	Flash     string
	Warning   string
	Error     string
}

>>>> pkg/opengraph/opengraph.go
package opengraph

import "fmt"

func generateOGTags(title, description, imageURL, pageURL string) string {
	return fmt.Sprintf(`
        <meta property="og:title" content="%s" />
        <meta property="og:description" content="%s" />
        <meta property="og:image" content="%s" />
        <meta property="og:url" content="%s" />
        <meta property="og:type" content="website" />
    `, title, description, imageURL, pageURL)
}

>>>> pkg/render/render.go
package render

import (
	"bytes"
	"fmt"
	"html/template"
	"log"
	"net/http"
	"path/filepath"

	"github.com/stagedates/shortlink-service/pkg/config"
	"github.com/stagedates/shortlink-service/pkg/models"
)

var functions = template.FuncMap{}

var app *config.AppConfig

// NewTemplates sets the config for the template package
func NewTemplates(a *config.AppConfig) {
	app = a
}

func AddDefaultData(td *models.TemplateData) *models.TemplateData {
	return td
}

// WriteTemplate renders a template
func WriteTemplate(w http.ResponseWriter, tmpl string, td *models.TemplateData) {
	var tc map[string]*template.Template

	if app.UseCache {
		// get the template cache from the app config
		tc = app.TemplateCache
	} else {
		tc, _ = CreateTemplateCache()
	}

	t, ok := tc[tmpl]
	if !ok {
		log.Fatal("Could not get template from template cache")
	}

	buf := new(bytes.Buffer)

	td = AddDefaultData(td)

	_ = t.Execute(buf, td)

	_, err := buf.WriteTo(w)
	if err != nil {
		fmt.Println("error writing template to browser", err)
	}

}

// CreateTemplateCache creates a template cache as a map
func CreateTemplateCache() (map[string]*template.Template, error) {

	myCache := map[string]*template.Template{}

	pages, err := filepath.Glob("./templates/*.page.tmpl")
	if err != nil {
		return myCache, err
	}

	for _, page := range pages {
		name := filepath.Base(page)
		ts, err := template.New(name).Funcs(functions).ParseFiles(page)
		if err != nil {
			return myCache, err
		}

		matches, err := filepath.Glob("./templates/*.layout.tmpl")
		if err != nil {
			return myCache, err
		}

		if len(matches) > 0 {
			ts, err = ts.ParseGlob("./templates/*.layout.tmpl")
			if err != nil {
				return myCache, err
			}
		}

		myCache[name] = ts
	}

	return myCache, nil
}

>>>> pkg/serviceclients/event_client.go
package serviceclients

import (
	"encoding/json"
	"fmt"
	"net/http"

	"gofr.dev/pkg/gofr"
)

// The most interesting properties for generating a preview
type Event struct {
	ID            string `json:"id"`
	EventID       string `json:"event_id"`
	Title         string `json:"title"`
	Subtitle      string `json:"subtitle"`
	Description   string `json:"description"`
	ShortCode     string `json:"shortCode"`
	ThumbnailURL  string `json:"thumbnailUrl"`
	CoverURL      string `json:"coverUrl"`
	Slug          string `json:"slug"`
	ThumbnailPath string `json:"thumbnailPath"`
	IsDraft       bool   `json:"isDraft"`
}

type EventsClient struct {
	baseURL string
}

func NewEventsClient(baseURL string) (*EventsClient, error) {
	return &EventsClient{
		baseURL: baseURL,
	}, nil
}

func (c *EventsClient) handleEventsSvcResponse(resp *http.Response) (*Event, error) {
	var event Event

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	if err := json.NewDecoder(resp.Body).Decode(&event); err != nil {
		return nil, err
	}

	return &event, nil
}

func (c *EventsClient) GetEvent(app *gofr.App, eventID string) (*Event, error) {
	url := fmt.Sprintf("%s/events/api/events/%s", c.baseURL, eventID)

	resp, err := http.Get(url)
	if err != nil {
		app.Logger().Errorf("failed to fetch event: %v", err)
		return nil, err
	}
	defer resp.Body.Close()

	return c.handleEventsSvcResponse(resp)
}

func (c *EventsClient) TestConnection() error {
	resp, err := http.Get(c.baseURL + "/events/api/health")
	if err != nil {
		return fmt.Errorf("failed to connect to events service: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code from events service: %d", resp.StatusCode)
	}

	return nil
}

>>>> pkg/validators/api_key_validator.go
package validators

import (
	"encoding/base64"
	"strings"

	"gofr.dev/pkg/gofr"
)

func ApiKeyValidator(app *gofr.App, apiKey any) bool {

	app.Logger().Debug("apiKeyValidator(): Validating API key")

	// Handle nil or empty API key
	if apiKey == nil {
		app.Logger().Warn("apiKeyValidator(): API key is nil")
		return false
	}

	// Convert apiKey to string safely
	apiKeyStr, ok := apiKey.(string)
	if !ok {
		app.Logger().Warn("apiKeyValidator(): API key is not a string")
		return false
	}

	if apiKeyStr == "" {
		app.Logger().Warn("apiKeyValidator(): API key is empty")
		return false
	}

	envApiKeys := app.Config.GetOrDefault("API_KEYS", "")

	// Decode API key and check matches
	for _, encodedKey := range strings.Split(envApiKeys, ",") {
		decodedBytes, err := base64.StdEncoding.DecodeString(encodedKey)
		if err == nil && string(decodedBytes) == apiKeyStr {
			app.Logger().Debug("apiKeyValidator(): Valid API key authenticated")
			return true
		}
	}

	app.Logger().Warn("apiKeyValidator(): Invalid API key attempted")
	return false
}

>>>> start.sh
#!/bin/bash

# ╔════════════════════════════════════════════════════════════════════════════════╗
# ║ Version:                                                                    03 ║
# ║ Date of Version:                                                    22.05.2025 ║
# ║ Owner:                                                                      SD ║
# ║ Classification:                                                       Internal ║
# ║ Distribution:                                                        All Staff ║
# ╚════════════════════════════════════════════════════════════════════════════════╝

set -eu -o pipefail
set -o posix
# set -o errexit   # abort on nonzero exitstatus
# set -o nounset   # abort on unbound variable
# set -o pipefail  # don't hide errors within pipes

script_path=$(realpath "${BASH_SOURCE[0]}")
script_path="${script_path%/*}"
cd "${script_path}"

source ./.env

{
    function usage(){
        printf "Usage:\n"
        printf "\t%s <subcommand>\n\n" ${0}
        printf "Subcommands:\n"
        printf "\t%10s\t\t%s\n" "--start-docker-compose-env" "Shoul be the very first step. Start a local postgresql and pubsub emulator in a docker compose environment"
        printf "\t%10s\t%s\n" "--start-local-short-link-service" "Starts the service locally, plain, without docker"
        echo
        printf "\t%10s\t\t%s\n" "--print-some-useful-endpoints" "Print some useful endpoints"
        echo
        printf "\nExperimental:\n"
        printf "\t%10s\t\t%s\n" "--init-local-db-for-shortlink-svc-dev-works" "..."
        printf "\t%10s\t\t\t%s\n" "--init-pubsub-emulator" "Add needed contents to local plain pubsub emulator"
        printf "\t%10s\t\t%s\n" "--start-local-pubsub-emulator" "Optionally starts a plain emulator instead of docker compose based"
        printf "\t%10s\t\t%s\n" "--forward-events-service" "Forward events service port to localhost"
      
        printf "\t%10s\t\t%s\n" "--start-docker-build-image" "Locally test building docker image"
	
    }

    function __init(){
        ln -s $HOME/s/tools_local/dev-environment/docker-compose.yaml
    }

    # TODO: add to: ~/s/tools_local/dev-environment/.env
    # TODO: followings have moved to .env. Deal with them later.
    # export PUBSUB_PORT="8087"
    # export PUBSUB_HOST="0.0.0.0"
    # export PUBSUB_HOST_PORT="${PUBSUB_HOST}:${PUBSUB_PORT}"


    # Inject  ~/s/tools_local/dev-environment/.env configs:
    # local dev db configuration
    # TODO: followings have moved to .env. Deal with them later.
    # export POSTGRES_USER=postgres
    # export POSTGRES_PASSWORD=postgres
    # export POSTGRES_DB=backend

    # SQL_DUMP=<path to backend_dev.sql dump>
    #GCLOUD_PROJECT=demo-prj
    # export GCLOUD_PROJECT=stdts-dev

    # export CONTAINER_NAME_PREFIX="shortlink-svc"

    function start_docker_compose_env(){
        source ./.env
        docker compose -f docker-compose.yaml up -d
    }
    function start_docker_build_image(){
        source ./.env
        docker build -t shortlink-test-build -f infrastructure/Dockerfile .
    }

    function start_pubsub_emulator_plain(){
        echo "Going to listen on ${PUBSUB_HOST_PORT} ..."
        gcloud beta emulators pubsub start \
            --host-port=${PUBSUB_HOST_PORT} \
            --data-dir=$PWD/mtdata/pubsubemulator
    }

    function start_pubsub_emulator_containerized(){
        # TODO: add data-dir param and bind-mount
        docker run --name=gcloud-emulator -d -p ${PUBSUB_PORT}:${PUBSUB_PORT} \
            gcr.io/google.com/cloudsdktool/google-cloud-cli:emulators gcloud beta emulators pubsub start --project=stdts-dev \
            --host-port=${PUBSUB_HOST_PORT}
    }

    function start_local_service(){
        exec gow run cmd/web/*.go
    }

    function init_pubsub_emulator(){
        PUBSUB_SCRIPT_PATH=$PWD/mtsrc/python-pubsub/samples/snippets
        TOPIC_ID=events.events
        SUB_ID=shortlink-worker.events.events

        echo "creating topics ..."
        python ${PUBSUB_SCRIPT_PATH}/publisher.py ${GCLOUD_PROJECT} create ${TOPIC_ID}
        
        echo "creating subscriptions ..."
        # TODO: continue here
        # ${SUB_ID} --topic=${TOPIC_ID}
        python subscriber.py ${GCLOUD_PROJECT} create generate-pdf-v1 eventarc-europe-west3-generate-pdf-function-293018-sub-782
    }

    function init_local_db_for_shortlink_svc_dev_works(){
        ## This Script content is originally from minikube:start.sh:initial_backend_setup()
        echo working on host $POSTGRES_HOST and port  $POSTGRES_PORT ...

        # close connections, so that we can re-create the db
        psql -U postgres -h $POSTGRES_HOST -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '"${POSTGRES_DB}"' AND leader_pid IS NULL;"

        psql -U postgres -h $POSTGRES_HOST -c "DROP DATABASE IF EXISTS backend;"
        psql -U postgres -h $POSTGRES_HOST -c "CREATE DATABASE backend;"

        # change the search_path, so that we work in the appropriate schema
        psql -U postgres -h $POSTGRES_HOST -d backend -c "ALTER ROLE ${POSTGRES_USER} SET search_path=shortlink;"
        psql -U postgres -h $POSTGRES_HOST -d backend -c "CREATE SCHEMA IF NOT EXISTS shortlink AUTHORIZATION ${POSTGRES_USER};"

        # in case we need to restore a dump
        # psql -U postgres -h $POSTGRES_HOST -d backend <./mt/db/backend_dev.sql >/dev/null 2>&1
    }

    function forward_events_service(){
        echo "Forwarding events service port to localhost ..."
        kubectl $CONTEXT -nstdts-dev port-forward svc/events-gke 9090:8080
    }

    function print_some_useful_endpoints(){
        printf "http://localhost:9092/health\n"
        printf "http://localhost:9090/events/api/health\n"
        printf "http://localhost:9090/events/api/swaggerui\n"
        printf "http://localhost:9090/events/api/events/8ca79956-8480-451f-afa8-07ab575b41a3\n"
        printf "http://localhost:9092/e/8ca79956-8480-451f-afa8-07ab575b41a3\n"
    }

    function import_testing_db_dump(){
        source ./.env
        echo "Importing testing db dump ..."
        psql -U postgres -h ${POSTGRES_HOST} -d backend -f ./local/init-db.sql
    }

    function fix_and_tidy_go_modules(){
        echo "Tidying go modules ..."
        go clean -modcache
        go get -u ./...
        go mod tidy
    }

    function main(){
        case ${1-usage} in
            --start-docker-compose-env)
                start_docker_compose_env
                sleep 2
                import_testing_db_dump
                sleep 2
                fix_and_tidy_go_modules
                sleep 2
                forward_events_service
                
                ;;

            --init-pubsub-emulator)
                init_pubsub_emulator
                ;;

            --start-local-short-link-service)
                start_local_service
                ;;

            --start-local-pubsub-emulator)
                start_pubsub_emulator_plain
                ;;

            --start-docker-build-image)
                start_docker_build_image
                ;;
	    
            --forward-events-service)
                forward_events_service
                ;;

            --print-some-useful-endpoints)
                print_some_useful_endpoints
                ;;

            --init-local-db-for-shortlink-svc-dev-works)
                init_local_db_for_shortlink_svc_dev_works
                ;;
        *)
            usage
            ;;
        esac
    }
}

{
    main "${@}"
    exit 0
}

>>>> templates/about.html
    <!doctype html>
    <html lang="en">

    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

        <title>My Nice Page</title>

        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" 
            integrity="sha384-B0vP5xmATw1+K9KRQjQERJvTumQW0nPEzvF6L/Z6nronJ3oUOFUFpCjEUQouq2+l" crossorigin="anonymous">

        <style>
            .my-footer {
                height: 5em;
                background-color: #163b65;
                margin-top: 1.5em;
                padding: 1em;
                color: white;
                font-size: 80%;
            }
        </style>
    </head>

    <body>

<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
  <a class="navbar-brand" href="#">Navbar</a>
  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <div class="collapse navbar-collapse" id="navbarNav">
    <ul class="navbar-nav">
      <li class="nav-item active">
        <a class="nav-link" href="index.html">Home <span class="sr-only">(current)</span></a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="about.html">About</a>
      </li>
      
    </ul>
  </div>
</nav>


    

    <div class="container">
        <div class="row">
            <div class="col">
                <h1 class="mt-3">This will be the about</h1>
        
            </div>
        </div>

    </div>


    <footer class="my-footer">
        <div class="row">
            <div class="col">
                left
            </div>

            <div class="col">
                center
            </div>

            <div class="col">
                right
            </div>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js" 
        integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" 
        crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js" 
        integrity="sha384-Piv4xVNRyMGpqkS2by6br4gNJ7DXjqk09RmUpJ8jgGtD7zP9yug3goQfGII0yAns" 
        crossorigin="anonymous"></script>


    </body>

    </html>
>>>> templates/about.page.tmpl
{{template "base" .}}

{{define "content"}}
    <div class="container">
        <div class="row">
            <div class="col">
                <h1>About short links</h1>
                <p>About short links</p>

                <p>The following comes from the template: {{index .StringMap "test"}}</p>

                <p>
                    {{if ne (index .StringMap "remote_ip") ""}}
                        Your remote ip address is {{index .StringMap "remote_ip"}}
                    {{else}}
                        I don't know your ip address yet. Please visit the <a href="/">home page</a> so I can get it.
                    {{end}}
                </p>
            </div>
        </div>
    </div>
{{end}}

>>>> templates/base.layout.tmpl
{{define "base"}}
    <!doctype html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport"
              content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <title>Short Links go Go</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css"
              integrity="sha384-B0vP5xmATw1+K9KRQjQERJvTumQW0nPEzvF6L/Z6nronJ3oUOFUFpCjEUQouq2+l"
              crossorigin="anonymous">

        {{block "css" .}}

        {{end}}
    </head>
    <body>

    {{block "content" .}}

    {{end}}


    {{block "js" .}}

    {{end}}

    </body>
    </html>
{{end}}
>>>> templates/gofr.html
    <!doctype html>
    <html lang="en">

    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

        <title>My Nice Page</title>

        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" 
            integrity="sha384-B0vP5xmATw1+K9KRQjQERJvTumQW0nPEzvF6L/Z6nronJ3oUOFUFpCjEUQouq2+l" crossorigin="anonymous">

        <style>
            .my-footer {
                height: 5em;
                background-color: #163b65;
                margin-top: 1.5em;
                padding: 1em;
                color: white;
                font-size: 80%;
            }
        </style>
    </head>

    <body>

<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
  <a class="navbar-brand" href="#">Navbar</a>
  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <div class="collapse navbar-collapse" id="navbarNav">
    <ul class="navbar-nav">
      <li class="nav-item active">
        <a class="nav-link" href="index.html">Home</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="about.html">About</a>
      </li>
      <li class="nav-item active">
        <a class="nav-link" href="gofr.html">GoFr <span class="sr-only">(current)</span></a>
      </li>
      
    </ul>
  </div>
</nav>


    

    <div class="container">
        <div class="row">
            <div class="col">
                <h1 class="mt-3">This will be about GoFr integration and usage</h1>
        
            </div>
        </div>

    </div>


    <footer class="my-footer">
        <div class="row">
            <div class="col">
                left
            </div>

            <div class="col">
                center
            </div>

            <div class="col">
                right
            </div>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js" 
        integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" 
        crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js" 
        integrity="sha384-Piv4xVNRyMGpqkS2by6br4gNJ7DXjqk09RmUpJ8jgGtD7zP9yug3goQfGII0yAns" 
        crossorigin="anonymous"></script>


    </body>

    </html>
>>>> templates/gofr.page.tmpl
{{template "base" .}}

{{define "content"}}
    <div class="container">
        <div class="row">
            <div class="col">
                <h1>About Gofr.dev</h1>

                <p>More details at https://gofr.dev</p>

            </div>
        </div>
    </div>
{{end}}

>>>> templates/home.page.tmpl
{{template "base" .}}

{{define "content"}}
    <div class="container">
        <div class="row">
            <div class="col">
                <h1>This is the home page of the short links</h1>
                <p>This is the home page of the short links</p>
            </div>
        </div>
    </div>
{{end}}

>>>> templates/index.html
    <!doctype html>
    <html lang="en">

    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

        <title>Short Links go Go</title>

        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" 
            integrity="sha384-B0vP5xmATw1+K9KRQjQERJvTumQW0nPEzvF6L/Z6nronJ3oUOFUFpCjEUQouq2+l" crossorigin="anonymous">

        <style>
            .my-footer {
                height: 5em;
                background-color: #163b65;
                margin-top: 1.5em;
                padding: 1em;
                color: white;
                font-size: 80%;
            }
        </style>
    </head>

    <body>

<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
  <a class="navbar-brand" href="#">Navbar</a>
  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <div class="collapse navbar-collapse" id="navbarNav">
    <ul class="navbar-nav">
      <li class="nav-item active">
        <a class="nav-link" href="index.html">Home <span class="sr-only">(current)</span></a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="about.html">About</a>
      </li>
      
    </ul>
  </div>
</nav>


    <div id="main-carousel" class="carousel slide" data-ride="carousel">

        <ol class="carousel-indicators">
            <li data-target="#main-carousel" data-slide-to="0" class="active"></li>
            <li data-target="#main-carousel" data-slide-to="1"></li>
            <li data-target="#main-carousel" data-slide-to="2"></li>
        </ol>

        <div class="carousel-inner">
            <div class="carousel-item active">
                <img src="static/images/woman-laptop.png" class="d-block w-100" alt="Woman and laptop">
                <div class="carousel-caption d-none d-md-block">
                    <h5>First slide label</h5>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                </div>
            </div>
            <div class="carousel-item">
                <img src="static/images/tray.png" class="d-block w-100" alt="Tray with coffee">
                <div class="carousel-caption d-none d-md-block">
                    <h5>Second slide label</h5>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                </div>
            </div>
            <div class="carousel-item">
                <img src="static/images/outside.png" class="d-block w-100" alt="Outside">
                <div class="carousel-caption d-none d-md-block">
                    <h5>Third slide label</h5>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <div class="col">
                <h1 class="text-center mt-4">Welcome!</h1>
                <p>
                    Welcome!
                </p>
            </div>
        </div>


        <div class="row">

            <div class="col text-center">

                <a href="/search-availability" class="btn btn-success">Make Reservation Now</a>

            </div>
        </div>

    </div>


    <footer class="my-footer">
        <div class="row">
            <div class="col">
                left
            </div>

            <div class="col">
                center
            </div>

            <div class="col">
                right
            </div>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js" 
        integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" 
        crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js" 
        integrity="sha384-Piv4xVNRyMGpqkS2by6br4gNJ7DXjqk09RmUpJ8jgGtD7zP9yug3goQfGII0yAns" 
        crossorigin="anonymous"></script>


    </body>

    </html>
>>>> templates/opengraph.page.html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Page Title</title>

    <meta property="og:title" content="Your Page Title">
    <meta property="og:description" content="A brief description of your page content">
    <meta property="og:image" content="https://example.com/path/to/your/image.jpg">
    <meta property="og:url" content="https://example.com/your-page-url">
    <meta property="og:type" content="website">

</head>
<body>
    <h1>Your Page Content</h1>
    <p>The rest of your page content goes here.</p>
</body>
</html>

>>>> templates/opengraph.partial.tmpl
    <meta property="og:title" content="%s" />
    <meta property="og:description" content="%s" />
    <meta property="og:image" content="%s" />
    <meta property="og:url" content="%s" />
    <meta property="og:type" content="website" />
>>>> testing/assert_api_is_protected.curl.sh
#!/bin/bash

function call_url_without_api_key(){
    printf "Expecting: \"Unauthorized: Authorization header missing\"\n"
    curl -X POST http://localhost:8000/links \
        -H "Content-Type: application/json" \
        -d '{
            "targetUrl": "https://zdnet.de"
        }'
}

function call_unprotected_endpoint() {
    printf "Calling unprotected endpoint\n"
    curl -X GET http://localhost:8000/.well-known/alive \
        -H "Content-Type: application/json"
        
}
function main(){
    call_url_without_api_key
    call_unprotected_endpoint
}

main


>>>> testing/assert_firebase_dynamic.curl.sh
#!/bin/bash

function usage(){
    echo "Error: target_testing_env argument is required"
    echo "Usage: $0 <local|dev>"
    echo "  local - Use http://localhost:8000"
    echo "  dev   - Use https://link.dev.stagedat.es"
    exit 1
}

# Check if target environment argument is provided
if [ -z "$1" ]; then
    usage && exit 1
fi

target_testing_env=$1

# Set TESTING_BASE_URL based on target environment
if [ "$target_testing_env" = "local" ]; then
    export TESTING_BASE_URL="http://localhost:8000"
elif [ "$target_testing_env" = "dev" ]; then
    export TESTING_BASE_URL="https://link.dev.stagedat.es"
else
    usage && exit 1
fi

echo "Using environment: $target_testing_env"
echo "Testing URL: $TESTING_BASE_URL"

function call_url(){
    local url=$1
    shift
    local msg=$1
    printf "Test Description: %s:\n%s\n" "${msg}" "${url}"
    curl -k -i $url
    echo
}

function test_existing_link(){
    call_url "${TESTING_BASE_URL}/jcoM" "Asserting existing dynamic short link behaviour"
}

function test_non_existing_link(){
    call_url ${TESTING_BASE_URL}/jcoMXXX "Asserting non-existing short link"
}

function main(){
    test_existing_link
    test_non_existing_link
}

main

>>>> testing/assert_firebase_link_creation.curl.sh
#!/bin/bash

# Create a Dynamic Link without source url pre-defined
function create_dynamic_link_without_source(){
  local msg="Create a Dynamic Link without a pre-defined source url ..."
  printf "%s\n" "${msg}"

  curl -k -X POST http://localhost:8000/links \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://stagedates.com/events/star-fm-rooftop-party-nhow-berlin-20250726-vDO1Q"    
  }'
}

function create_dynamic_link_with_source(){
  local msg="Create a Dynamic Link with a pre-defined source url ..."
  printf "%s\n" "${msg}"

  curl -k -X POST http://localhost:8000/links \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://stagedates.com/events/star-fm-rooftop-party-nhow-berlin-20250726-vDO1Q",
    "shortlink": "https://link.dev.stagedat.es/abcd"
  }'

}

create_dynamic_link_without_source
create_dynamic_link_with_source

>>>> testing/assert_link_insertion.curl.sh
#!/bin/bash

curl -X POST https://link.dev.stagedat.es:8080/links \
  -H "Content-Type: application/json" \
  -d '{
    "type": "event",
    "path": "/e/coldplay-westfalenhallen",
    "original_url": "https://dev.stagedat.es/events/coldplay-westfalenhallen-20241218-GsQE7"
  }'


curl -X POST https://link.dev.stagedat.es/links \
  -H "Content-Type: application/json" \
  -d '{
    "type": "event",
    "path": "/e/eternal-sunshine-tour-2025-lanxess-arena",
    "original_url": "https://dev.stagedat.es/events/eternal-sunshine-tour-2025-lanxess-arena-20250616-6bTZR"
  }'

>>>> testing/assert_redirection_link.curl.sh
#!/bin/bash

curl -i https://link.dev.stagedat.es:9092/r/948e6ef2-7574-4aa5-a639-4144d506e48b
