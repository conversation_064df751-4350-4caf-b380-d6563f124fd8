# Beware: this environment file is for the local start wrapper, not for the service environment itself

export PUBSUB_PORT="8087"
export PUBSUB_HOST="0.0.0.0"
export PUBSUB_HOST_PORT="${PUBSUB_HOST}:${PUBSUB_PORT}"

export GCLOUD_PROJECT=stdts-dev

export CONTAINER_NAME_PREFIX="shortlink-svc"

# for docker-compose.yaml
export REDIS_PORT=6379

# ^ The following is for start.sh:start_local_service() ^
#export GOOGLE_APPLICATION_CREDENTIALS=$HOME/.config/gcloud/application_default_credentials.json
export APP_ENV="local"
export PUBSUB_EMULATOR_HOST=${PUBSUB_HOST_PORT}
# $ start.sh:start_local_service() $


# ^ db configuration - postgresql

# this is for scripting reasons around the application, like dev database management.
# the application database settings live in the according k8s config map 
# or in /configs/.local.env, in this order. 

export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=postgres
export POSTGRES_DB=backend
export POSTGRES_PORT=5432
export POSTGRES_HOST=127.0.0.1

# $ db configuration

# Kubernetes Context. This is optional. Set it to empty, if not needed.
export CONTEXT=

