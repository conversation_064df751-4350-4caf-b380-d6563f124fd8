package opengraph

import "fmt"

func generateOGTags(title, description, imageURL, pageURL string) string {
	return fmt.Sprintf(`
        <meta property="og:title" content="%s" />
        <meta property="og:description" content="%s" />
        <meta property="og:image" content="%s" />
        <meta property="og:url" content="%s" />
        <meta property="og:type" content="website" />
    `, title, description, imageURL, pageURL)
}
