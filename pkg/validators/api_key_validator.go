package validators

import (
	"encoding/base64"
	"strings"

	"gofr.dev/pkg/gofr"
)

func ApiKeyValidator(app *gofr.App, apiKey any) bool {

	app.Logger().Debug("apiKeyValidator(): Validating API key")

	// Handle nil or empty API key
	if apiKey == nil {
		app.Logger().Warn("apiKeyValidator(): API key is nil")
		return false
	}

	// Convert apiKey to string safely
	apiKeyStr, ok := apiKey.(string)
	if !ok {
		app.Logger().Warn("apiKeyValidator(): API key is not a string")
		return false
	}

	if apiKeyStr == "" {
		app.Logger().Warn("apiKeyValidator(): API key is empty")
		return false
	}

	envApiKeys := app.Config.GetOrDefault("API_KEYS", "")

	// Decode API key and check matches
	for _, encodedKey := range strings.Split(envApiKeys, ",") {
		decodedBytes, err := base64.StdEncoding.DecodeString(encodedKey)
		if err == nil && string(decodedBytes) == apiKeyStr {
			app.Logger().Debug("apiKeyValidator(): Valid API key authenticated")
			return true
		}
	}

	app.Logger().Warn("apiKeyValidator(): Invalid API key attempted")
	return false
}
