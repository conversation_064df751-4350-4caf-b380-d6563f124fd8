package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"regexp"

	"github.com/stagedates/shortlink-service/pkg/serviceclients"
	"gofr.dev/pkg/gofr"
)

var uuidRegex = regexp.MustCompile(`^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`)

type Event struct {
	ID            string `json:"id"`
	EventID       string `json:"event_id"`
	Title         string `json:"title"`
	Subtitle      string `json:"subtitle"`
	Description   string `json:"description"`
	ShortCode     string `json:"shortCode"`
	ThumbnailURL  string `json:"thumbnailUrl"`
	CoverURL      string `json:"coverUrl"`
	Slug          string `json:"slug"`
	ThumbnailPath string `json:"thumbnailPath"`
	IsDraft       bool   `json:"isDraft"`
}

type EventClient interface {
	GetEvent(*gofr.App, string) (*Event, error)
}

type RawResponse struct {
	Content string `json:"content"`
	Type    string `json:"type"`
}

func (r RawResponse) MarshalJSON() ([]byte, error) {
	// Create a map to hold the content
	data := map[string]interface{}{
		"data": map[string]string{
			"content": r.Content,
			"type":    r.Type,
		},
	}
	// Marshal the map to JSON
	return json.Marshal(data)
}

func (r RawResponse) GetContentType() string {
	return r.Type
}

// EventPreviewLinkHandler is a handler for event preview links
// It generates the html document for the event preview
func EventPreviewLinkHandler(app *gofr.App) func(*gofr.Context) (interface{}, error) {
	return func(c *gofr.Context) (interface{}, error) {
		identifier := c.PathParam("identifier")
		if identifier == "" {
			return nil, fmt.Errorf("identifier parameter is required")
		}

		event, err := ProcessEventIdentifier(app, identifier)
		if err != nil {
			return nil, err
		}

		// Cast the event to the correct type
		eventData, ok := event.(*Event)
		if !ok {
			return nil, fmt.Errorf("invalid event data type")
		}

		// Generate the OpenGraph HTML
		html := OpenGraph(
			eventData.Title,
			eventData.Description,
			eventData.ThumbnailURL,
			eventData.Slug,
		)

		// Return a raw response
		return RawResponse{
			Content: html,
			Type:    "text/html",
		}.Content, nil
	}
}

func ProcessEventIdentifier(app *gofr.App, identifier string) (interface{}, error) {
	if isUUID(identifier) {
		return processEventByUUID(app, identifier)
	}
	return processEventBySlug(app, identifier)
}

// Helper functions (implement these based on your needs)
func isUUID(str string) bool {
	return uuidRegex.MatchString(str)
}

func processEventByUUID(app *gofr.App, uuid string) (interface{}, error) {
	app.Logger().Infof("Processing event with UUID: %s", uuid)

	// Get event from events service
	eventsClient, err := serviceclients.NewEventsClient(app.Config.GetOrDefault("EVENTS_SERVICE_BASE_URL", "http://localhost:8080"))
	event, err := eventsClient.GetEvent(app, uuid)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch event: %v", err)
	}

	return event, nil
}

func processEventBySlug(app *gofr.App, slug string) (interface{}, error) {
	log.Printf("Processing event with slug: %s", slug)

	eventsClient, err := serviceclients.NewEventsClient(app.Config.GetOrDefault("EVENTS_SERVICE_BASE_URL", "http://localhost:8080"))

	// Get event from events service
	event, err := eventsClient.GetEvent(app, slug)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch event: %v", err)
	}

	return event, nil
}

// TODO:
func main_event_processing_from_web(eventsClient interface{}, gofrapp *gofr.App, msg []byte) {
	var payload struct {
		Data struct {
			ID string `json:"id"`
		} `json:"data"`
	}

	if err := json.Unmarshal(msg, &payload); err == nil && payload.Data.ID != "" {
		gofrapp.Logger().Infof("ID: %s\n", payload.Data.ID)

		// Fetch event details using GetEvent
		event, err := eventsClient.(interface {
			GetEvent(*gofr.App, string) (*Event, error)
		}).GetEvent(gofrapp, payload.Data.ID)
		if err != nil {
			gofrapp.Logger().Errorf("Error getting event: %v", err)
		} else {
			gofrapp.Logger().Infof("\n--- Event Details ---")
			gofrapp.Logger().Infof("Title: %s", event.Title)
			gofrapp.Logger().Infof("Subtitle: %s", event.Subtitle)
			gofrapp.Logger().Infof("Description: %s", event.Description)
			gofrapp.Logger().Infof("Thumbnail URL: %s", event.ThumbnailURL)
			gofrapp.Logger().Infof("Cover URL: %s", event.CoverURL)
			gofrapp.Logger().Infof("Slug: %s", event.Slug)
			gofrapp.Logger().Infof("Thumbnail Path: %s", event.ThumbnailPath)
			gofrapp.Logger().Infof("Is Draft: %v", event.IsDraft)

			gofrapp.Logger().Infof(OpenGraph(event.Title, event.Description, event.ThumbnailURL, event.Slug))
		}
	}
}
