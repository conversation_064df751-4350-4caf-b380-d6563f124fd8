// https://gofr.dev/docs/advanced-guide/using-publisher-subscriber#example
package handlers

import (
	"fmt"

	"cloud.google.com/go/pubsub"
	"gofr.dev/pkg/gofr"

	"context"
	"time"
)

//var HandleMessagesWithGooglesPubSubClient func(gofrapp *gofr.App, ctx context.Context, subscriptionID, projectID string) error

func StartGooglesEventPubSubProcessor(gofrapp *gofr.App) error {

	projectID := "stdts-dev"
	topicId := "events.events"
	subID := "shortlink-worker.events.events"

	ctx := context.Background()

	// Creates a client.
	client, err := pubsub.NewClient(ctx, projectID)
	if err != nil {
		gofrapp.Logger().Fatalf("Failed to create client: %v", err)
	}

	topic := client.Topic(topicId)
	defer client.Close()

	client.CreateSubscription(ctx, subID, pubsub.SubscriptionConfig{
		Topic:       topic,
		AckDeadline: 20 * time.Second,
	})

	if err != nil {
		gofrapp.Logger().<PERSON>rrorf("CreateSubscription: %w", err)
	}

	return nil
}

// handleMessage processes incoming Pub/Sub messages.
func HandleMessagesWithGooglesPubSubClient(gofrapp *gofr.App, ctx context.Context, subscriptionID, projectID string, msgHandler func([]byte)) error {
	client, err := pubsub.NewClient(ctx, projectID)
	if err != nil {
		return fmt.Errorf("pubsub.NewClient: %v", err)
	}
	defer client.Close()

	sub := client.Subscription(subscriptionID)
	err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
		msgHandler(msg.Data)
		msg.Ack()
	})

	return err
}

/*
func HandleMessagesWithGooglesPubSubClient(gofrapp *gofr.App, ctx context.Context, subscriptionID, projectID string) error {
	gofrapp.Logger().Info("HandleMessagesWithGooglesPubSubClient(): starting ...")
	client, err := pubsub.NewClient(ctx, projectID)
	if err != nil {
		gofrapp.Logger().Errorf("pubsub.NewClient: %w", err)
	}
	defer client.Close()

	sub := client.Subscription(subscriptionID)

	err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
		gofrapp.Logger().Infof("\n--- Message received at %v ---\n", time.Now().Format(time.RFC3339))
		gofrapp.Logger().Infof("Data: %s\n", string(msg.Data))

		// Parse message as JSON to check for ID
		// and pass it to the opengraph handler
		var payload struct {
			Data struct {
				ID          string `json:"id"`
				Title       string `json:"title"`
				Description string `json:"description"`
				ImageURL    string `json:"image_url"`
				PageURL     string `json:"page_url"`
			} `json:"data"`
		}

		if err := json.Unmarshal(msg.Data, &payload); err == nil {
			if payload.Data.ID != "" {
				gofrapp.Logger().Infof("ID: %s\n", payload.Data.ID)
			}

			// Generate OpenGraph tags if we have the required fields
			ogTags := generateOGTags(
				payload.Data.Title,
				payload.Data.Description,
				payload.Data.ImageURL,
				payload.Data.PageURL,
			)

			gofrapp.Logger().Infof("Generated OpenGraph Tags:\n%s", ogTags)
		}

		if len(msg.Attributes) > 0 {
			gofrapp.Logger().Info("Attributes:")
			for key, value := range msg.Attributes {
				gofrapp.Logger().Infof("  %s: %s\n", key, value)
			}
		}
		gofrapp.Logger().Infof("PublishTime: %v\n", msg.PublishTime)
		gofrapp.Logger().Infof("Message ID: %s\n", msg.ID)
		msg.Ack()
	})

	if err != nil {
		return fmt.Errorf("sub.Receive: %w", err)
	}

	gofrapp.Logger().Info("HandleMessagesWithGooglesPubSubClient(): leaving ...")
	return nil
} */

// DEPR - Optional implementation of the Pub/Sub subscriber
func StartGofrEventPubSubProcessor(gofrapp *gofr.App) {
	topic := "events.events"
	gofrapp.Subscribe(topic, func(c *gofr.Context) error {
		var orderStatus struct {
			OrderId string `json:"orderId"`
			Status  string `json:"status"`
		}

		err := c.Bind(&orderStatus)
		if err != nil {
			c.Logger.Error(err)

			// returning nil here as we would like to ignore the
			// incompatible message and continue reading forward
			return nil
		}

		c.Logger.Info("Received order ", orderStatus)

		return nil
	})
}
