package handlers

import (
	"fmt"
)

func generateOGTags(title, description, imageURL, pageURL string) string {
	return fmt.Sprintf(`
        <meta property="og:title" content="%s" />
        <meta property="og:description" content="%s" />
        <meta property="og:image" content="%s" />
        <meta property="og:url" content="%s" />
        <meta property="og:type" content="website" />
    `, title, description, imageURL, pageURL)
}

// O<PERSON> is the handler for the open graph page
// func (m *Repository) OpenGraph(w http.ResponseWriter, r *http.Request) {
func OpenGraph(title, description, imageURL, pageURL string) string {

	// stringMap := make(map[string]string)
	// stringMap["test"] = "CHANGEME"

	////////////////// ^

	// Get the current page URL
	//pageURL := "https://website.com" + r.URL.Path

	// Generate or get the image URL for this page
	//imageURL := "https://website.com/images/og-image.jpg" // TODO: set current url

	// Generate Open Graph tags
	ogTags := generateOGTags(title, description, imageURL, pageURL)

	// Set the content type
	//w.Header().Set("Content-Type", "text/html; charset=utf-8")

	// Write the HTML response with Open Graph tags
	//fmt.Fprintf(w, `
	return fmt.Sprintf(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>%s</title>
        %s
    </head>
    <body>
        <h1>%s</h1>
    </body>
    </html>
    `, title, ogTags, title)

	////////////////// $

	//render.RenderTemplate(w, "opengraph.partial.tmpl", &models.TemplateData{
	//	StringMap: stringMap,
	//})
}
