package handlers

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/url"
	"time"

	"gofr.dev/pkg/gofr"
	"gofr.dev/pkg/gofr/container"
	"gofr.dev/pkg/gofr/http/response"
)

const (
	defaultDbTimeout       = time.Duration(5) * time.Second
	queryRetrieveTargetUrl = "SELECT original_url FROM link WHERE id = $1"
)

var (
	ErrShortLinkNotFound           = errors.New("short link not found")
	ErrShortLinkIdentifierNotFound = errors.New("short link identifier not found in database")
	ErrFailedToRetrieveTargetUrl   = errors.New("failed to retrieve target URL")
	ErrInvalidUrl                  = errors.New("invalid URL format")
)

func EventRedirectionHandler(ctx *gofr.Context) (interface{}, error) {
	identifier := ctx.PathParam("identifier")
	if identifier == "" {
		return nil, fmt.Errorf("identifier parameter is required")
	}

	fmt.Printf("Processing redirection for identifier: %s\n", identifier)

	queryCtx, cancel := context.WithTimeout(context.Background(), defaultDbTimeout)
	defer cancel()

	var targetUrl string
	targetUrl, err := retrieveOriginalURL(queryCtx, ctx.SQL, identifier)
	if err != nil {
		return nil, err
	}

	if err := validateURL(targetUrl); err != nil {
		return nil, err
	}

	ctx.Logger.Info("Successfully redirected to URL", "original_url", targetUrl)

	return response.Redirect{
		URL: targetUrl,
	}, nil
}

func retrieveOriginalURL(ctx context.Context, db container.DB, identifier string) (string, error) {
	var originalUrl string // DEPRECATED: use SrcURL instead

	if err := db.QueryRowContext(ctx, queryRetrieveTargetUrl, identifier).Scan(&originalUrl); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return "", ErrShortLinkIdentifierNotFound
		}
		return "", ErrFailedToRetrieveTargetUrl
	}

	if originalUrl == "" {
		return "", ErrShortLinkNotFound
	}

	return originalUrl, nil
}

func validateURL(urlStr string) error {
	parsedUrl, err := url.Parse(urlStr)

	if err != nil || parsedUrl.Scheme == "" || parsedUrl.Host == "" {
		return ErrInvalidUrl
	}

	return nil
}
