package config

import (
	"html/template"
	"log"

	"github.com/alexedwards/scs/v2"
)

// AppConfig holds the application config
type AppConfig struct {
	UseCache               bool
	TemplateCache          map[string]*template.Template
	InfoLog                *log.Logger
	InProduction           bool
	Session                *scs.SessionManager
	EventsServiceAvailable bool
	// TODO: add flag for telemetry service available
	// TODO: add flag for pubsub service available
}
