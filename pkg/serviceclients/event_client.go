package serviceclients

import (
	"encoding/json"
	"fmt"
	"net/http"

	"gofr.dev/pkg/gofr"
)

// The most interesting properties for generating a preview
type Event struct {
	ID            string `json:"id"`
	EventID       string `json:"event_id"`
	Title         string `json:"title"`
	Subtitle      string `json:"subtitle"`
	Description   string `json:"description"`
	ShortCode     string `json:"shortCode"`
	ThumbnailURL  string `json:"thumbnailUrl"`
	CoverURL      string `json:"coverUrl"`
	Slug          string `json:"slug"`
	ThumbnailPath string `json:"thumbnailPath"`
	IsDraft       bool   `json:"isDraft"`
}

type EventsClient struct {
	baseURL string
}

func NewEventsClient(baseURL string) (*EventsClient, error) {
	return &EventsClient{
		baseURL: baseURL,
	}, nil
}

func (c *EventsClient) handleEventsSvcResponse(resp *http.Response) (*Event, error) {
	var event Event

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	if err := json.NewDecoder(resp.Body).Decode(&event); err != nil {
		return nil, err
	}

	return &event, nil
}

func (c *EventsClient) GetEvent(app *gofr.App, eventID string) (*Event, error) {
	url := fmt.Sprintf("%s/events/api/events/%s", c.baseURL, eventID)

	resp, err := http.Get(url)
	if err != nil {
		app.Logger().Errorf("failed to fetch event: %v", err)
		return nil, err
	}
	defer resp.Body.Close()

	return c.handleEventsSvcResponse(resp)
}

func (c *EventsClient) TestConnection() error {
	resp, err := http.Get(c.baseURL + "/events/api/health")
	if err != nil {
		return fmt.Errorf("failed to connect to events service: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code from events service: %d", resp.StatusCode)
	}

	return nil
}
