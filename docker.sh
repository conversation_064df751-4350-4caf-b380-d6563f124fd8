#!/bin/bash

IMAGE="v61"

function start_docker_build_image(){
    source ./.env
    printf "docker build \
-t shortlink-test-build \
-t europe-west3-docker.pkg.dev/stdts-dev/shortlink-service/shortlink-service:latest \
-t europe-west3-docker.pkg.dev/stdts-dev/shortlink-service/shortlink-service:$IMAGE \
-f infrastructure/Dockerfile .\n"
}

function start_docker_push_image(){
    source ./.env
    printf "docker push \
europe-west3-docker.pkg.dev/stdts-dev/shortlink-service/shortlink-service:$IMAGE\n"
    printf "docker push \
europe-west3-docker.pkg.dev/stdts-dev/shortlink-service/shortlink-service:latest\n"

    printf "kubectl \${CONTEXT} \${NS} set image deployment/shortlink shortlink=europe-west3-docker.pkg.dev/stdts-dev/shortlink-service/shortlink-service:$IMAGE\n"
    printf "kubectl \${CONTEXT} \${NS} rollout status deployment/shortlink \n"
    printf "kubectl \${CONTEXT} \${NS} rollout history deployment/shortlink \n"
    printf "kubectl \${CONTEXT} \${NS} rollout undo deployment/shortlink \n"
    printf "kubectl \${CONTEXT} \${NS} rollout status deployment/shortlink --revision=150\n"

    
}

start_docker_build_image
start_docker_push_image
